# 支付页面接口修改总结

## 修改背景

由于 `GetCheckoutInfo` 接口报错，将支付页面的数据获取接口改为使用 `GetOrderDetail` 接口来获取订单详情信息。

## 主要修改内容

### 1. 接口替换

**修改前：**
```javascript
import { GetCheckoutInfo } from '@/api/checkout'
import { GetOrderPayInfo } from '@/api/order'
```

**修改后：**
```javascript
import { GetOrderDetail } from '@/api/order'
```

### 2. 数据获取逻辑重构

**修改前：**
- 有订单号：调用 `GetOrderPayInfo`
- 无订单号：调用 `GetCheckoutInfo`

**修改后：**
- 必须有订单号：调用 `GetOrderDetail`
- 无订单号：显示错误提示

### 3. 核心函数修改

#### 新的 `fetchOrderDetail` 函数：

```javascript
const fetchOrderDetail = async () => {
    if (!orderInfo.orderNumber) {
        ElMessage.error('订单号不能为空')
        return
    }
    
    try {
        loading.value = true
        const response = await GetOrderDetail(orderInfo.orderNumber)
        
        if (response.status === 'success' && response.data && response.data.order) {
            const order = response.data.order
            
            // 更新订单信息
            Object.assign(orderInfo, {
                orderNumber: order.number,
                amount: order.total || 0,
                currency: order.currency_code || 'usd',
                amountFormat: order.total_format || ''
            })
            
            // 根据订单中的支付方式代码设置可用支付方式
            if (order.payment_method_code) {
                const paymentCode = order.payment_method_code
                const paymentName = order.payment_method_name || ''
                
                if (paymentMethodsConfig[paymentCode]) {
                    // 只显示订单指定的支付方式
                    availablePaymentMethods.value = [{
                        ...paymentMethodsConfig[paymentCode],
                        code: paymentCode,
                        name: paymentName || paymentMethodsConfig[paymentCode].name
                    }]
                    selectedPaymentMethod.value = paymentMethodsConfig[paymentCode].id
                } else {
                    // 支付方式不在配置中，显示所有可用支付方式
                    availablePaymentMethods.value = Object.values(paymentMethodsConfig)
                    selectedPaymentMethod.value = availablePaymentMethods.value[0]?.id || ''
                }
            } else {
                // 订单没有指定支付方式，显示所有可用支付方式
                availablePaymentMethods.value = Object.values(paymentMethodsConfig)
                selectedPaymentMethod.value = availablePaymentMethods.value[0]?.id || ''
            }
        }
    } catch (error) {
        console.error('获取订单详情失败:', error)
        ElMessage.error('获取订单详情失败，请检查订单号是否正确')
    } finally {
        loading.value = false
    }
}
```

### 4. 支付方式处理逻辑

**智能支付方式显示：**

1. **订单已指定支付方式且在配置中**：只显示该支付方式
2. **订单已指定支付方式但不在配置中**：显示所有可用支付方式
3. **订单未指定支付方式**：显示所有可用支付方式

### 5. 数据结构对应关系

| GetOrderDetail 字段 | 用途 | 对应前端字段 |
|-------------------|------|-------------|
| `order.number` | 订单号 | `orderInfo.orderNumber` |
| `order.total` | 订单总金额 | `orderInfo.amount` |
| `order.total_format` | 格式化金额 | `orderInfo.amountFormat` |
| `order.currency_code` | 货币代码 | `orderInfo.currency` |
| `order.payment_method_code` | 支付方式代码 | 用于匹配支付方式配置 |
| `order.payment_method_name` | 支付方式名称 | 显示在支付选项中 |

## 优势

### 1. 数据一致性
- 直接从订单详情获取准确的订单信息
- 避免了多个接口数据不一致的问题

### 2. 简化逻辑
- 只需要一个接口调用
- 减少了接口依赖和错误处理复杂度

### 3. 更好的用户体验
- 根据订单实际支付方式智能显示选项
- 提供明确的错误提示

## 使用方式

### 正确的跳转方式：
```javascript
// 从订单列表或订单详情页跳转
router.push({
    path: '/pay',
    query: { orderNumber: 'ORDER123456' }
})
```

### 错误处理：
- 如果没有提供订单号，页面会显示错误提示
- 如果订单号无效，会显示"获取订单详情失败"的提示

## 兼容性

- ✅ 保持与现有支付组件的完全兼容
- ✅ 支持所有已配置的支付方式（PayPal、Stripe、BitPay、微信支付）
- ✅ 保持原有的支付流程不变

## 注意事项

1. **必须提供订单号**：支付页面现在必须通过 URL 参数提供订单号
2. **订单状态**：建议只对未支付的订单显示支付页面
3. **支付方式配置**：确保 `paymentMethodsConfig` 中包含所有可能的支付方式代码

## 测试建议

1. 测试有效订单号的情况
2. 测试无效订单号的错误处理
3. 测试不同支付方式代码的显示逻辑
4. 测试订单未指定支付方式的情况

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>语言初始化测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
            background-color: #f9f9f9;
        }
        .test-result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 3px;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        .current-settings {
            background-color: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <h1>🌍 语言初始化功能测试</h1>
    
    <div class="current-settings">
        <h3>当前设置</h3>
        <div id="current-browser-lang">浏览器语言: <span id="browser-lang-value">-</span></div>
        <div id="current-stored-lang">本地存储语言: <span id="stored-lang-value">-</span></div>
        <div id="current-detected-lang">检测到的语言: <span id="detected-lang-value">-</span></div>
    </div>

    <div class="test-section">
        <h3>🧪 测试功能</h3>
        <button onclick="testBrowserLanguageDetection()">测试浏览器语言检测</button>
        <button onclick="testLanguageConversion()">测试语言代码转换</button>
        <button onclick="testLanguageValidation()">测试语言验证</button>
        <button onclick="testFirstVisitScenario()">模拟首次访问</button>
        <button onclick="testReturnVisitScenario()">模拟再次访问</button>
        <button onclick="clearStorage()">清除本地存储</button>
    </div>

    <div id="test-results"></div>

    <script>
        // 模拟语言初始化功能（简化版本）
        const SUPPORTED_LANGUAGES = [
            { id: 0, name: 'Español', lang: 'es' },
            { id: 1, name: 'Français', lang: 'fr' },
            { id: 2, name: 'Indonesia', lang: 'id' },
            { id: 3, name: 'Italiano', lang: 'it' },
            { id: 4, name: '日本語', lang: 'ja' },
            { id: 5, name: '한국어', lang: 'ko' },
            { id: 6, name: 'Русский', lang: 'Py' },
            { id: 7, name: '繁體中文', lang: 'hk' },
            { id: 8, name: 'Deutsch', lang: 'de' },
            { id: 9, name: '简体中文', lang: 'zh' },
            { id: 10, name: 'English', lang: 'en' },
        ];

        function getBrowserLanguage() {
            const browserLang = navigator.language || navigator.userLanguage;
            let mainLang = browserLang.toLowerCase().split('-')[0];
            
            if (mainLang === 'zh') {
                if (
                    browserLang.toLowerCase().includes('cn') ||
                    browserLang.toLowerCase().includes('hans')
                ) {
                    return 'zh';
                } else if (
                    browserLang.toLowerCase().includes('tw') ||
                    browserLang.toLowerCase().includes('hk') ||
                    browserLang.toLowerCase().includes('hant')
                ) {
                    return 'hk';
                }
                return 'zh';
            }
            
            const isSupported = SUPPORTED_LANGUAGES.some((item) => item.lang === mainLang);
            return isSupported ? mainLang : 'en';
        }

        function getStoredLanguage() {
            return localStorage.getItem('language');
        }

        function setStoredLanguage(lang) {
            localStorage.setItem('language', lang);
        }

        function isValidLanguage(lang) {
            return SUPPORTED_LANGUAGES.some((item) => item.lang === lang);
        }

        function getLanguageName(lang) {
            const langItem = SUPPORTED_LANGUAGES.find((item) => item.lang === lang);
            return langItem ? langItem.name : 'English';
        }

        function addTestResult(message, type = 'info') {
            const resultsDiv = document.getElementById('test-results');
            const resultDiv = document.createElement('div');
            resultDiv.className = `test-result ${type}`;
            resultDiv.innerHTML = message;
            resultsDiv.appendChild(resultDiv);
        }

        function clearResults() {
            document.getElementById('test-results').innerHTML = '';
        }

        function updateCurrentSettings() {
            document.getElementById('browser-lang-value').textContent = navigator.language;
            document.getElementById('stored-lang-value').textContent = getStoredLanguage() || '无';
            document.getElementById('detected-lang-value').textContent = getBrowserLanguage();
        }

        function testBrowserLanguageDetection() {
            clearResults();
            addTestResult('<h4>🔍 浏览器语言检测测试</h4>');
            
            const browserLang = navigator.language;
            const detectedLang = getBrowserLanguage();
            const langName = getLanguageName(detectedLang);
            
            addTestResult(`浏览器语言: ${browserLang}`, 'info');
            addTestResult(`检测到的语言代码: ${detectedLang}`, 'info');
            addTestResult(`语言名称: ${langName}`, 'info');
            
            if (isValidLanguage(detectedLang)) {
                addTestResult('✅ 检测到的语言代码有效', 'success');
            } else {
                addTestResult('❌ 检测到的语言代码无效', 'error');
            }
        }

        function testLanguageConversion() {
            clearResults();
            addTestResult('<h4>🔄 语言代码转换测试</h4>');
            
            const testCases = [
                { frontend: 'zh', api: 'zh_cn', description: '简体中文' },
                { frontend: 'hk', api: 'zh_hk', description: '繁体中文' },
                { frontend: 'en', api: 'en', description: '英文' },
            ];
            
            testCases.forEach(({ frontend, api, description }) => {
                // 简化的转换逻辑
                const toApi = frontend === 'zh' ? 'zh_cn' : frontend === 'hk' ? 'zh_hk' : frontend;
                const fromApi = api === 'zh_cn' ? 'zh' : api === 'zh_hk' ? 'hk' : api;
                
                const toApiCorrect = toApi === api;
                const fromApiCorrect = fromApi === frontend;
                
                addTestResult(
                    `${toApiCorrect ? '✅' : '❌'} ${description} 前端->API: ${frontend} -> ${toApi} (期望: ${api})`,
                    toApiCorrect ? 'success' : 'error'
                );
                addTestResult(
                    `${fromApiCorrect ? '✅' : '❌'} ${description} API->前端: ${api} -> ${fromApi} (期望: ${frontend})`,
                    fromApiCorrect ? 'success' : 'error'
                );
            });
        }

        function testLanguageValidation() {
            clearResults();
            addTestResult('<h4>✅ 语言验证测试</h4>');
            
            const validLanguages = ['en', 'zh', 'hk', 'fr', 'de', 'es', 'it', 'ja', 'ko', 'id', 'Py'];
            const invalidLanguages = ['xx', 'invalid', '', null, undefined];
            
            validLanguages.forEach(lang => {
                const isValid = isValidLanguage(lang);
                addTestResult(
                    `${isValid ? '✅' : '❌'} 有效语言: ${lang}`,
                    isValid ? 'success' : 'error'
                );
            });
            
            invalidLanguages.forEach(lang => {
                const isValid = isValidLanguage(lang);
                addTestResult(
                    `${!isValid ? '✅' : '❌'} 无效语言: ${lang}`,
                    !isValid ? 'success' : 'error'
                );
            });
        }

        function testFirstVisitScenario() {
            clearResults();
            addTestResult('<h4>🆕 首次访问场景测试</h4>');
            
            // 清除本地存储模拟首次访问
            localStorage.removeItem('language');
            
            const detectedLang = getBrowserLanguage();
            setStoredLanguage(detectedLang);
            
            addTestResult('模拟首次访问：清除本地存储', 'info');
            addTestResult(`检测浏览器语言: ${navigator.language} -> ${detectedLang}`, 'info');
            addTestResult(`保存到本地存储: ${detectedLang}`, 'success');
            addTestResult(`语言名称: ${getLanguageName(detectedLang)}`, 'info');
            
            updateCurrentSettings();
        }

        function testReturnVisitScenario() {
            clearResults();
            addTestResult('<h4>🔄 再次访问场景测试</h4>');
            
            const storedLang = getStoredLanguage();
            
            if (storedLang) {
                if (isValidLanguage(storedLang)) {
                    addTestResult(`从本地存储读取语言: ${storedLang}`, 'success');
                    addTestResult(`语言名称: ${getLanguageName(storedLang)}`, 'info');
                    addTestResult('✅ 使用保存的语言设置', 'success');
                } else {
                    addTestResult(`本地存储的语言无效: ${storedLang}`, 'error');
                    const fallbackLang = getBrowserLanguage();
                    addTestResult(`回退到浏览器语言: ${fallbackLang}`, 'info');
                    setStoredLanguage(fallbackLang);
                }
            } else {
                addTestResult('本地存储中没有语言设置', 'error');
                addTestResult('请先运行"模拟首次访问"测试', 'info');
            }
            
            updateCurrentSettings();
        }

        function clearStorage() {
            localStorage.removeItem('language');
            addTestResult('✅ 已清除本地存储', 'success');
            updateCurrentSettings();
        }

        // 页面加载时更新当前设置
        window.onload = function() {
            updateCurrentSettings();
            addTestResult('🎉 页面加载完成，可以开始测试！', 'info');
        };
    </script>
</body>
</html>

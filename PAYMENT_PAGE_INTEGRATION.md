# 支付页面接口对接说明

## 修改概述

已将支付页面的接口从 `GetCheckoutInfo` 改为使用 `GetOrderDetail` 接口，使支付页面能够展示真实的订单数据，并根据订单中的支付方式信息动态显示对应的支付选项。

## 主要修改内容

### 1. 接口对接

- **GetOrderDetail**: 获取订单详情信息，包含订单金额、支付方式、货币等完整信息

### 2. 数据结构优化

#### 新增响应式数据：

```javascript
const checkoutData = ref(null); // 结算数据
const availablePaymentMethods = ref([]); // 可用支付方式
const loading = ref(false); // 加载状态
```

#### 支付方式映射配置：

```javascript
const paymentMethodsConfig = {
  paypal: { id: 'paypal', name: 'PayPal', icon: '/src/assets/paypal.png' },
  stripe: { id: 'stripe', name: 'Stripe', icon: '/src/assets/<EMAIL>' },
  bitpay: { id: 'bitpay', name: 'BitPay', icon: '/src/assets/<EMAIL>' },
  wepay: { id: 'wepay', name: '微信支付', icon: '/src/assets/<EMAIL>' },
  wechat: { id: 'wechat', name: '微信支付', icon: '/src/assets/<EMAIL>' },
};
```

### 3. 核心功能实现

#### 数据获取逻辑：

1. **必须有订单号**: 调用 `GetOrderDetail` 获取订单详情信息
2. **无订单号**: 显示错误提示，并显示所有可用支付方式

#### 支付方式处理：

- 根据订单中的 `payment_method_code` 和 `payment_method_name` 确定支付方式
- 如果订单已指定支付方式且在配置中，则只显示该支付方式
- 如果订单未指定支付方式或支付方式不在配置中，则显示所有可用支付方式
- 使用配置映射将后端支付方式代码转换为前端组件

#### 金额显示优化：

- 优先使用后端返回的格式化金额 `amount_format`
- 支持多币种显示（USD、CNY 等）

### 4. 用户界面改进

#### 动态支付方式显示：

```vue
<el-radio-group v-model="selectedPaymentMethod" class="payment-options">
    <el-radio 
        v-for="method in availablePaymentMethods" 
        :key="method.id"
        :value="method.id" 
        class="payment-option"
    >
        <div class="payment-option-content">
            <img :src="method.icon" :alt="method.name" class="payment-logo" />
            <span>{{ method.name }}</span>
        </div>
    </el-radio>
</el-radio-group>
```

#### 加载状态和空状态处理：

- 添加加载动画 `v-loading="loading"`
- 无支付方式时显示提示信息

## 接口数据结构

### GetOrderDetail 返回数据：

```json
{
  "status": "success",
  "data": {
    "order": {
      "id": "123",
      "number": "ORDER123456",
      "total": 100.0,
      "total_format": "$100.00",
      "currency_code": "USD",
      "payment_method_code": "paypal",
      "payment_method_name": "PayPal",
      "status": "unpaid",
      "order_products": [
        {
          "id": "1",
          "name": "Product Name",
          "quantity": 1,
          "price": 100.0,
          "price_format": "$100.00"
        }
      ]
    }
  }
}
```

## 使用方式

### 从订单页跳转（必须有订单号）

```javascript
router.push({
  path: '/pay',
  query: { orderNumber: 'ORDER123456' },
});
// 自动调用 GetOrderDetail 获取订单详情信息
```

### 注意事项

- 支付页面现在**必须**提供订单号才能正常工作
- 如果没有订单号，页面会显示错误提示

## 错误处理

- 接口调用失败时显示错误提示
- 无可用支付方式时显示友好提示
- 支持重试机制

## 兼容性说明

- 保持与现有支付组件的兼容性
- 支持 Stripe、PayPal、BitPay、微信支付等多种支付方式
- 向后兼容原有的 URL 参数传递方式

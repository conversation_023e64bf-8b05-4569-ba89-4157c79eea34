# 状态管理使用指南

## 概述

本项目使用 Pinia 作为状态管理库，并提供了完整的用户认证状态管理方案。包含以下组件：

- 用户状态管理 Store (`src/stores/user.js`)
- localStorage 工具函数 (`src/utils/storage.js`)
- 认证相关工具函数 (`src/utils/auth.js`)
- 用户认证 Composable (`src/composables/useAuth.js`)
- 路由守卫 (`src/router.js`)

## 用户状态管理

### 1. 用户 Store (useUserStore)

```javascript
// 引入用户 Store
import { useUserStore } from '@/stores/user';

const userStore = useUserStore();

// 状态
userStore.token; // 用户token
userStore.userInfo; // 用户信息
userStore.isLoggedIn; // 是否已登录

// 方法
userStore.login(data); // 登录
userStore.logout(); // 登出
userStore.setToken(token); // 设置token
userStore.setUserInfo(info); // 设置用户信息
userStore.updateUserInfo(newInfo); // 更新用户信息
```

### 2. 使用 useAuth Composable (推荐)

```vue
<template>
  <div>
    <div v-if="isLoggedIn">
      欢迎，{{ userInfo?.name || '用户' }}！
      <button @click="handleLogout" :loading="loading">退出登录</button>
    </div>
    <div v-else>
      <button @click="goToLogin">去登录</button>
    </div>
  </div>
</template>

<script setup>
import { useAuth } from '@/composables/useAuth';

const { isLoggedIn, userInfo, loading, handleLogout, requireAuth } = useAuth();

const goToLogin = () => {
  requireAuth('请先登录以继续操作');
};

// 检查是否需要登录
const doSomething = () => {
  if (!requireAuth()) return;

  // 执行需要登录的操作
  console.log('执行操作...');
};
</script>
```

## localStorage 管理

### 基础 Storage 操作

```javascript
import {
  setStorage,
  getStorage,
  removeStorage,
  clearStorage,
} from '@/utils/storage';

// 存储数据
setStorage('key', 'value');
setStorage('user', { name: 'John', age: 30 });

// 获取数据
const value = getStorage('key', 'defaultValue');
const user = getStorage('user', null);

// 删除数据
removeStorage('key');

// 清空所有数据
clearStorage();
```

### 用户相关 Storage

```javascript
import { getToken, setToken, getUserInfo, setUserInfo } from '@/utils/storage';

// Token 操作
const token = getToken();
setToken('new-token');

// 用户信息操作
const userInfo = getUserInfo();
setUserInfo({ name: 'John', email: '<EMAIL>' });
```

## 认证工具函数

```javascript
import {
  isAuthenticated,
  isTokenValid,
  getUserRole,
  hasPermission,
  isAdmin,
  getUserDisplayName,
} from '@/utils/auth';

// 检查登录状态
if (isAuthenticated()) {
  console.log('用户已登录');
}

// 检查token有效性
if (isTokenValid()) {
  console.log('Token有效');
}

// 权限检查
if (hasPermission('edit_product')) {
  console.log('有编辑商品权限');
}

// 管理员检查
if (isAdmin()) {
  console.log('是管理员');
}

// 获取用户显示名称
const displayName = getUserDisplayName();
```

## 路由守卫

系统自动配置了路由守卫，以下路由需要登录：

- `/mine/*` - 个人中心相关页面
- `/wish` - 心愿单
- `/confirm` - 确认订单
- `/pay` - 支付页面

### 添加需要登录的路由

```javascript
// 在 router.js 中为路由添加 meta.requiresAuth
{
  path: '/protected-page',
  component: () => import('./pages/ProtectedPage.vue'),
  meta: {
    requiresAuth: true, // 需要登录
    title: '受保护的页面'
  }
}
```

## 在组件中使用

### 登录页面示例

```vue
<script setup>
import { useAuth } from '@/composables/useAuth';
import { reactive } from 'vue';

const { handleLogin, loginLoading } = useAuth();

const loginForm = reactive({
  email: '',
  password: '',
});

const doLogin = async () => {
  try {
    await handleLogin(loginForm);
    // 登录成功，会自动跳转
  } catch (error) {
    // 登录失败，已自动显示错误消息
  }
};
</script>
```

### 需要登录的页面

```vue
<script setup>
import { useAuth } from '@/composables/useAuth';
import { onMounted } from 'vue';

const { requireAuth, isLoggedIn, userInfo } = useAuth();

onMounted(() => {
  // 页面加载时检查登录状态
  if (!requireAuth('访问此页面需要登录')) {
    return;
  }

  // 执行需要登录的逻辑
  loadUserData();
});

const loadUserData = () => {
  console.log('加载用户数据...', userInfo.value);
};
</script>
```

## HTTP 请求自动携带 Token

`src/utils/request.js` 已配置自动携带 token：

```javascript
// 请求时自动添加 Authorization header
// 无需手动处理

// 响应拦截器自动处理401错误
// 自动清除用户状态并跳转登录页
```

## 最佳实践

1. **优先使用 useAuth Composable**：提供了完整的认证功能和状态管理

2. **使用路由守卫**：为需要登录的页面添加 `meta.requiresAuth: true`

3. **错误处理**：所有认证相关操作都有完善的错误处理和用户提示

4. **状态持久化**：用户状态自动保存到 localStorage，刷新页面不会丢失

5. **自动重定向**：登录成功后自动跳转到原来要访问的页面

6. **统一的用户信息管理**：所有用户相关数据都通过 Store 统一管理

## 数据结构

### 用户信息结构

```javascript
{
  token: 'jwt-token-string',
  user: {
    id: 1,
    name: 'John Doe',
    email: '<EMAIL>',
    role: 'user', // 'user', 'admin', 'super_admin'
    permissions: ['read', 'write'], // 权限数组
    avatar: 'https://example.com/avatar.jpg'
  }
}
```

### 登录/注册 API 响应格式

后端应返回包含以下字段的数据：

```javascript
{
  status: 'success',
  data: {
    token: 'jwt-token',
    user: {
      // 用户信息
    }
  }
}
```

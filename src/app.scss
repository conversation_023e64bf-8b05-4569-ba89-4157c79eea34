.header {
  background: #ffffff;
  width: 100%;
  height: 8rem;
  color: #242426;
  box-sizing: border-box;
  padding: 0 9.7rem;
  position: sticky;
  top: 0;
  z-index: 99;

  // 移动端适配
  &.header-mobile {
    height: 6rem;
    padding: 0 1.5rem;
  }

  // 平板端适配
  &.header-tablet {
    height: 7rem;
    padding: 0 3rem;
  }
  &_view {
    height: 8.5rem;
    margin: 0 auto;
    display: flex;
    align-items: center;
    justify-content: space-between;
    color: #ffffff;
    position: relative;

    // 移动端适配
    .header-mobile & {
      height: 6rem;
    }

    // 平板端适配
    .header-tablet & {
      height: 7rem;
    }
    &_logo {
      height: 3.1rem;
      width: auto;

      // 移动端适配
      .header-mobile & {
        height: 2.5rem;
      }

      // 平板端适配
      .header-tablet & {
        height: 2.8rem;
      }
    }
    &_nav {
      display: flex;
      align-items: center;
      gap: 3.5rem;

      // 移动端菜单样式
      .header-mobile & {
        position: fixed;
        top: 6rem;
        left: 0;
        right: 0;
        background: #ffffff;
        flex-direction: column;
        gap: 0;
        // padding: 2rem 0;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        transform: translateY(-100%);
        opacity: 0;
        visibility: hidden;
        transition: all 0.3s ease;
        z-index: 998;
        height: calc(100vh - 6rem);

        &.mobile-nav-open {
          transform: translateY(0);
          opacity: 1;
          visibility: visible;
        }
      }

      // 平板端适配
      .header-tablet & {
        gap: 2.5rem;
      }

      &_item {
        cursor: pointer;
        font-size: 2rem;
        color: #242426;
        font-weight: 500;
        box-sizing: border-box;

        // 移动端导航项样式
        .header-mobile & {
          width: 100%;
          // text-align: center;
          padding: 1.5rem 2rem;
          padding-left: 2rem;
          border-bottom: 1px solid #f0f0f0;
          font-size: 1.8rem;

          &:last-child {
            border-bottom: none;
          }

          &:hover,
          &:active {
            background-color: #f8f9fa;
          }
        }

        // 平板端导航项样式
        .header-tablet & {
          font-size: 1.8rem;
        }

        &:hover {
          color: #6e4aeb;
        }
      }
    }

    // 移动端菜单按钮
    .mobile-menu-btn {
      display: none;
      flex-direction: column;
      justify-content: space-around;
      width: 2.4rem;
      height: 2.4rem;
      cursor: pointer;
      padding: 0.3rem 0;

      .header-mobile & {
        display: flex;
      }

      .menu-line {
        width: 100%;
        height: 0.2rem;
        background-color: #242426;
        border-radius: 0.1rem;
        transition: all 0.3s ease;
      }

      &:hover .menu-line {
        background-color: #6e4aeb;
      }
    }

    &_action {
      display: flex;
      align-items: center;
      justify-content: flex-end;
      gap: 2.9rem;

      // 移动端操作栏样式
      &.mobile-actions {
        gap: 1.5rem;
      }
      &_item {
        cursor: pointer;
        position: relative;
        font-family: PingFang SC, PingFang SC;
        font-weight: 400;
        font-size: 1.5rem;
        color: #242426;
        display: flex;
        align-items: center;
        cursor: pointer;

        // 移动端操作项样式
        .mobile-actions & {
          font-size: 1.4rem;
        }

        &_arrow {
        }
        &_icon {
          width: 2.3rem;
          height: 2.3rem;

          // 移动端图标样式
          .mobile-actions & {
            width: 2rem;
            height: 2rem;
          }
        }
        &_menu {
          display: none;
          flex-direction: column;
          padding: 0.8rem 0;
          width: 8.7rem;
          position: absolute;
          top: 2.3rem;
          left: -2rem;
          background: #ffffff;
          box-shadow: 0px 0.4rem 1rem 0px rgba(0, 0, 0, 0.1);
          border-radius: 0.8rem;
          &_item {
            margin-bottom: 0.4rem;
            height: 2rem;
            font-family: PingFang SC, PingFang SC;
            font-weight: 400;
            font-size: 1.4rem;
            color: #242426;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.8rem;
            cursor: pointer;
            &_icon {
              width: 1.6rem;
              height: 1.6rem;
            }
          }
          &_item:hover {
            background: #f3f4f8;
          }
        }
      }
      .unit {
        margin-right: 2rem;
      }
      .lang {
        margin-left: -2.9rem;
      }
      .lang:hover {
        .header_view_action_item_arrow {
          transform: rotate(180deg);
        }
        .header_view_action_item_menu {
          display: flex;
        }
      }
      .unit:hover {
        .header_view_action_item_arrow {
          transform: rotate(180deg);
        }
        .header_view_action_item_menu {
          display: flex;
        }
      }
      &_line {
        width: 2.3rem;
        height: 0rem;
        border: 0.1rem solid #d5d5d5;
        transform: rotate(90deg);
      }
    }
  }
}
.footer {
  background-color: #202020;
  padding: 9.7rem 30.1rem;
  box-sizing: border-box;
  display: flex;
  align-items: center;
  justify-content: space-between;

  // 移动端适配
  &.footer-mobile {
    padding: 4rem 2rem;
    flex-direction: column;
    gap: 4rem;
    align-items: flex-start;
    text-align: left;
  }
  &_main {
    // 移动端适配
    .footer-mobile & {
      width: 100%;
    }

    &_title {
      height: 5.4rem;
      line-height: 5.4rem;
      font-family: PingFang SC, PingFang SC;
      font-weight: 600;
      font-size: 3.9rem;
      color: #ffffff;
      margin-bottom: 1.2rem;

      // 移动端适配
      .footer-mobile & {
        font-size: 2.8rem;
        height: auto;
        line-height: 1.2;
        margin-bottom: 1.5rem;
      }
    }
    &_tip {
      height: 2.7rem;
      line-height: 2.7rem;
      font-family: PingFang SC, PingFang SC;
      font-weight: 400;
      font-size: 1.9rem;
      color: #ffffff;

      // 移动端适配
      .footer-mobile & {
        font-size: 1.6rem;
        height: auto;
        line-height: 1.4;
        margin-bottom: 1rem;
      }
    }
    &_link {
      font-family: PingFang SC, PingFang SC;
      font-weight: 400;
      font-size: 1.9rem;
      color: #ffffff;
      cursor: pointer;

      // 移动端适配
      .footer-mobile & {
        font-size: 1.6rem;
        margin-bottom: 2rem;
      }

      span {
        text-decoration: underline;
      }
    }
    &_concat {
      display: flex;
      align-items: center;
      margin-top: 6.5rem;

      // 移动端适配
      .footer-mobile & {
        margin-top: 2rem;
        gap: 1.5rem;
      }
      &_f {
        background: rgba(255, 255, 255, 0.1);
        border-radius: 3.9rem;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        margin-right: 2.3rem;
        width: 5rem;
        height: 5rem;
        border-radius: 100%;
        &_icon {
          width: 2.3rem;
          height: 2.3rem;
        }
      }
      &_item {
        cursor: pointer;
        margin-right: 2.3rem;
        width: 5rem;
        height: 5rem;
      }
    }
  }
  &_info {
    width: 654px;

    // 移动端适配
    .footer-mobile & {
      width: 100%;
    }

    &_title {
      height: 5.4rem;
      line-height: 5.4rem;
      font-family: PingFang SC, PingFang SC;
      font-weight: 600;
      font-size: 3.9rem;
      color: #ffffff;
      margin-bottom: 12px;

      // 移动端适配
      .footer-mobile & {
        font-size: 2.8rem;
        height: auto;
        line-height: 1.2;
        margin-bottom: 1.5rem;
      }
    }
    &_desc {
      height: 2.7rem;
      line-height: 2.7rem;
      font-family: PingFang SC, PingFang SC;
      font-weight: 400;
      font-size: 1.9rem;
      color: #ffffff;
      margin-bottom: 3.3rem;

      // 移动端适配
      .footer-mobile & {
        font-size: 1.6rem;
        height: auto;
        line-height: 1.4;
        margin-bottom: 2rem;
      }
    }
    &_action {
      display: flex;
      align-items: center;
      height: 5.8rem;
      margin-bottom: 3.3rem;

      // 移动端适配
      .footer-mobile & {
        flex-direction: column;
        gap: 1.5rem;
        height: auto;
        margin-bottom: 2rem;
      }

      &_input {
        width: 45.2rem;
        height: 5.8rem;
        border-radius: 3.1rem;
        border: 0.1rem solid #ffffff;
        padding: 0 2.7rem;
        box-sizing: border-box;
        font-family: PingFang SC, PingFang SC;
        font-weight: 400;
        font-size: 1.9rem;
        color: #000000;

        // 移动端适配
        .footer-mobile & {
          width: 100%;
          height: 4.8rem;
          font-size: 1.6rem;
          padding: 0 2rem;
        }
      }
      &_btn {
        width: 20rem;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-left: 2.9rem;
        height: 5.8rem;
        background: #ffffff;
        border-radius: 3.1rem;
        font-family: PingFang SC, PingFang SC;
        font-weight: 600;
        font-size: 1.9rem;
        color: #000000;
        cursor: pointer;

        // 移动端适配
        .footer-mobile & {
          width: 100%;
          height: 4.8rem;
          margin-left: 0;
          font-size: 1.6rem;
        }

        &_icon {
          margin-left: 12px;
          width: 2.3rem;
          height: 2.3rem;
        }
      }
    }
    &_tip {
      height: 72px;
      line-height: 36px;
      font-family: PingFang SC, PingFang SC;
      font-weight: 400;
      font-size: 1.4rem;
      color: #ffffff;

      // 移动端适配
      .footer-mobile & {
        height: auto;
        line-height: 1.5;
        font-size: 1.3rem;
        text-align: left;
      }
    }
  }
}
.isEmpty {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 1.5rem;
  height: 100%;
  padding-bottom: 18rem;

  &_img {
    width: 18rem;
    height: 18rem;
    margin-bottom: 1rem;
  }

  &_text {
    font-size: 2.4rem;
    font-weight: 500;
    color: #333;
  }

  &_subtext {
    font-size: 1.6rem;
    color: #70707b;
    margin-bottom: 2rem;
    margin-top: -1rem;
  }

  &_action {
    width: 24rem;
    height: 4.8rem;
    background: linear-gradient(90deg, #6e4aeb, #7e5af7);
    color: white;
    vertical-align: top;
    border-radius: 8rem;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    font-family: PingFang SC;
    outline: 0;
    text-align: center;
    font-size: 1.8rem;
    color: #ffffff;
    font-weight: bold;
  }
}
.drawer_header {
  height: 8.5rem;
  position: relative;
  padding: 0 2.3rem;
  box-sizing: border-box;
  &_main {
    height: 8.5rem;
    display: flex;
    align-items: center;
    font-family: PingFang SC, PingFang SC;
    font-weight: 400;
    font-size: 1.9rem;
    color: #242426;
    border-bottom: 0.1rem solid #e7e7e7;
  }
  &_icon {
    position: absolute;
    right: 2.3rem;
    top: 50%;
    transform: translateY(-50%);
    width: 2.3rem;
    height: 2.3rem;
    cursor: pointer;
  }
}
.cart_view {
  &_item {
    display: flex;
    align-items: center;
    padding: 3rem 0;
    position: relative;
    border-bottom: 0.1rem solid #e7e7e7;
    &_checkbox {
      margin-right: 10px;
      color: rgb(255, 119, 0);
    }
    &_img {
      width: 10rem;
      height: 10rem;
      margin-right: 10px;
    }
    &_main {
      padding-left: 2rem;
      display: flex;
      flex-direction: column;
      box-sizing: border-box;
    }
    &_name {
      height: 2.2rem;
      line-height: 2.2rem;
      font-family: PingFang SC, PingFang SC;
      font-weight: 600;
      font-size: 1.6rem;
      color: #000000;
      margin-bottom: 1.2rem;
    }
    &_price {
      height: 2.8rem;
      line-height: 2.8rem;
      font-family: PingFang SC, PingFang SC;
      font-weight: 600;
      font-size: 2rem;
      color: #000000;
      margin-bottom: 1.4rem;
    }
    &_del {
      position: absolute;
      cursor: pointer;
      top: 50%;
      transform: translateY(-50%);
      right: 0;
      width: 2.4rem;
      height: 2.4rem;
    }
  }
  &_footer {
    display: flex;
    align-items: flex-start;
    flex-direction: column;
    padding: 0 20px;
    &_submit {
      margin: 2.4rem auto;
      width: 100%;
      height: 4.8rem;
      background: #6e4aeb;
      border-radius: 3.2rem;
      display: flex;
      align-items: center;
      justify-content: center;
      color: #ffffff;
      font-weight: 600;
      font-size: 1.8rem;
      cursor: pointer;
    }
    &_cart {
      width: 100%;
      height: 4.8rem;
      background: #f6f6f6;
      border-radius: 3.2rem;
      display: flex;
      align-items: center;
      justify-content: center;
      color: #242426;
      border: 0.1rem solid #e7e7e7;
      box-sizing: border-box;
      font-family: PingFang SC, PingFang SC;
      font-weight: 400;
      font-size: 1.8rem;
      cursor: pointer;
    }
  }
}
.currency-symbol {
  margin-right: 1.8rem;
}

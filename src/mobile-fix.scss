/* 移动端样式修复 - 解决文字过大问题 */

/* Element Plus 移动端全局样式 */
.el-select-dropdown {
  max-width: 90vw !important;

  .el-select-dropdown__item {
    font-size: 1.6rem !important;
    height: 4rem !important;
    line-height: 4rem !important;
    padding: 0 1.2rem !important;
  }
}

/* 全局滚动行为修复 */
@media screen and (max-width: 768px) {
  html,
  body {
    position: relative;
    overflow-y: auto !important;
    -webkit-overflow-scrolling: touch !important;
    height: auto !important;
    overscroll-behavior-y: none; /* 防止iOS橡皮筋效果导致的问题 */
  }

  /* 确保移动端导航栏固定在顶部 */
  .header {
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    right: 0 !important;
    z-index: 99 !important;
    width: 100% !important;
    background: #ffffff !important;
    -webkit-backface-visibility: hidden !important; /* 防止iOS中的闪烁 */
    backface-visibility: hidden !important;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1) !important; /* 添加阴影增强视觉效果 */
  }

  /* 路由视图容器添加顶部内边距 */
  .router-view-container {
    margin-top: 0 !important; /* 移除顶部外边距 */
    padding-top: 6rem !important; /* 改用内边距，视觉上更自然 */
  }

  /* 确保移动端菜单跟随导航栏 */
  .header_view_nav.mobile-nav-open {
    position: fixed !important;
    top: 6rem !important; /* 与移动端头部高度对应 */
    left: 0 !important;
    right: 0 !important;
    z-index: 98 !important;
    height: calc(100vh - 6rem) !important;
    overflow-y: auto !important;
    -webkit-overflow-scrolling: touch !important;
    margin-top: 0 !important; /* 确保没有额外的边距 */
  }

  /* 确保移动端菜单跟随导航栏 */
  .header_view_nav.mobile-nav-open {
    position: fixed !important;
    top: 6rem !important; /* 与移动端头部高度对应 */
    left: 0 !important;
    right: 0 !important;
    z-index: 98 !important;
    height: calc(100vh - 6rem) !important;
    overflow-y: auto !important;
    -webkit-overflow-scrolling: touch !important;
  }

  #app {
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    overflow: hidden;
  }

  .app-content {
    flex: 1;
    overflow-y: auto;
    -webkit-overflow-scrolling: touch;
  }

  /* 常规文本大小 */
  body {
    font-size: 1.4rem;
  }

  /* 标题大小调整 */
  h1,
  .h1-size {
    font-size: 2.2rem !important;
  }

  h2,
  .h2-size {
    font-size: 2rem !important;
  }

  h3,
  .h3-size {
    font-size: 1.8rem !important;
  }

  /* 按钮文本大小 */
  button,
  .btn {
    font-size: 1.4rem !important;
  }

  /* 导航菜单项 */
  .header_view_nav_item {
    font-size: 1.6rem !important;
  }

  /* 产品介绍区域 */
  .home_view_introduce {
    &_title {
      font-size: 2.2rem !important;
    }

    &_desc {
      font-size: 1.6rem !important;
    }

    &_list_item {
      &_name {
        font-size: 2.2rem !important;
      }

      &_desc {
        font-size: 1.4rem !important;
      }
    }
  }

  /* Ledger介绍区域 */
  .home_view_ledger {
    &_main {
      p {
        font-size: 1.6rem !important;
      }

      .puper {
        font-size: 1.8rem !important;
      }
    }
  }

  /* 商品列表 */
  .goods_item {
    &_title {
      font-size: 1.6rem !important;
    }

    &_price {
      font-size: 1.8rem !important;
    }

    &_desc {
      font-size: 1.4rem !important;
    }
  }

  /* 按钮样式调整 */
  .home_view_product_title_btn {
    padding: 0 1.5rem !important;
    height: 4rem !important;
  }

  /* 商品列表页面样式调整 */
  .goods-list-main {
    .banner-box_text {
      &_title {
        font-size: 2.6rem !important;
        line-height: 1.3 !important;
      }

      &_desc {
        font-size: 1.6rem !important;
        line-height: 1.5 !important;
      }
    }

    .goods_view {
      &_title {
        font-size: 2.4rem !important;
        line-height: 1.3 !important;
      }
    }
  }

  /* 商品列表组件样式调整 */
  .product_list {
    /* 非单列模式下的商品列表样式（Goods和Wish页面） */
    &:not(.single-column) {
      display: grid !important;
      grid-template-columns: 1fr 1fr !important; /* 两列布局 */
      gap: 1rem !important;
    }

    /* 单列模式下的商品列表样式（首页） */
    &.single-column {
      .product_list_item {
        width: 100% !important;
      }
    }

    &_item {
      height: auto !important;
      padding: 1.5rem !important;

      /* 只在非单列模式下应用宽度100% */
      &:not(.single-column-item) {
        width: 100% !important;
      }

      &_img {
        margin-bottom: 1rem !important;
        height: auto !important;
        // max-height: 15rem !important;
        width: 100% !important;
        object-fit: cover !important;
        align-self: center;
      }

      &_name {
        font-size: 1.6rem !important;
        height: auto !important;
        line-height: 1.3 !important;
        margin-bottom: 1rem !important;
        max-height: 3.6rem !important;
        overflow: hidden !important;
        display: -webkit-box !important;
        -webkit-line-clamp: 2 !important;
        -webkit-box-orient: vertical !important;
        line-clamp: 2 !important; /* 标准属性，配合-webkit-前缀实现兼容性 */
        white-space: normal !important;
      }

      &_info {
        gap: 0.5rem !important;
        flex-wrap: wrap !important;

        &_price {
          font-size: 1.4rem !important;
          height: 2.4rem !important;
          padding: 0 0.6rem !important;
        }

        &_sale {
          font-size: 1.4rem !important;
        }
      }
    }

    /* 单列图片样式特殊处理 */
    &.single-column .product_list_item_img {
      width: 100% !important;
      // max-width: 25rem !important;
      // max-height: 25rem !important;
      object-fit: contain !important;
    }
  }

  /* 收藏夹页面样式调整 */
  .wish_box {
    height: auto !important;
    padding: 3rem 1.5rem !important;

    &_title {
      font-size: 2.6rem !important;
      height: auto !important;
      line-height: 1.3 !important;
      margin-bottom: 1rem !important;
    }

    &_desc {
      font-size: 1.6rem !important;
      height: auto !important;
      line-height: 1.5 !important;
    }
  }

  .wish_view {
    padding: 2rem 1rem !important;
  }

  .empty_state {
    padding: 2rem !important;
    font-size: 1.4rem !important;
  }

  /* 商品详情页面移动端样式 */
  .goods_box {
    padding-top: 1.5rem !important;
    height: auto !important;
    overflow-y: auto !important;
    position: relative !important;
    -webkit-overflow-scrolling: touch !important; /* 增强iOS滑动体验 */

    &_title {
      margin-bottom: 1.5rem !important;
      padding: 0 1.5rem !important;
      font-size: 1.2rem !important;
    }

    &_info {
      padding: 0 1rem !important;
      flex-direction: column !important;

      /* 图片展示区域样式 */
      &_img {
        width: 100% !important;
        flex-direction: column !important;
        margin-bottom: 2rem !important;

        /* 大图区域放在上方 */
        &_big {
          width: 100% !important;
          margin-bottom: 1rem !important;
          order: 1; /* 改变DOM顺序，让大图在前 */

          &_img {
            width: 100% !important;
            height: auto !important;
            max-height: 30rem !important;
            object-fit: contain !important;
          }
        }

        /* 小图预览区放在下方，水平滚动 */
        &_list {
          width: 100% !important;
          height: auto !important;
          display: flex !important; /* 确保是flex布局 */
          flex-direction: row !important;
          flex-wrap: nowrap !important; /* 确保不换行 */
          overflow-x: auto !important;
          overflow-y: hidden !important;
          margin-right: 0 !important;
          order: 2; /* 改变DOM顺序，让小图列表在后 */
          padding: 0.8rem 0 !important;
          white-space: nowrap !important;
          -webkit-overflow-scrolling: touch !important; /* 平滑滚动 */
          overscroll-behavior-x: contain !important; /* 阻止过度滚动 */
          scroll-snap-type: x proximity !important; /* 提供平滑滚动停靠 */

          /* 隐藏滚动条但保持功能 */
          &::-webkit-scrollbar {
            display: none !important;
          }
          scrollbar-width: none !important; /* Firefox */

          &_single {
            width: 8rem !important;
            height: 8rem !important;
            min-width: 8rem !important; /* 添加最小宽度，确保不会被挤压 */
            margin: 0 0.5rem !important;
            display: inline-flex !important;
            align-items: center !important;
            justify-content: center !important;
            border-radius: 0.5rem !important;

            &_img {
              width: 7rem !important; /* 稍微小于容器，确保有边距 */
              height: 7rem !important;
              object-fit: contain !important; /* 保持图片比例 */
            }
          }
        }
      }

      /* 商品信息区域样式 */
      &_base {
        height: auto !important;
        padding-left: 0 !important;
        margin-bottom: 5rem;

        &_name {
          font-size: 2rem !important;
          margin-bottom: 0.5rem !important;
        }

        &_price {
          font-size: 1.8rem !important;
          height: 3rem !important;
          line-height: 3rem !important;
          margin-bottom: 1.5rem !important;
        }

        &_property {
          margin-bottom: 0.5rem !important;
          font-size: 1.4rem !important;
        }

        &_action {
          flex-wrap: nowrap !important; /* 不换行 */
          gap: 1rem !important;
          margin-top: 1.5rem !important;
          justify-content: space-between !important; /* 按钮两端对齐 */

          &_item {
            width: 48% !important; /* 设置为略小于50%，留出间距 */
            height: 4.5rem !important;
            font-size: 1.4rem !important; /* 略微减小字体 */
          }

          &_item.addcart {
            margin-right: 0 !important;
          }
        }

        &_collect {
          margin-top: 1rem !important;
          font-size: 1.6rem !important;
        }
      }
    }

    &_description {
      padding: 1.5rem !important;
      margin-top: 1.5rem !important;
      width: 100% !important;
      overflow: auto !important;
    }

    /* 确保内容区域可滚动 */
    &_content {
      width: 100% !important;
      height: auto !important;
      overflow: visible !important;
    }
  }

  /* 确认订单页面移动端样式 */
  .cart_view {
    padding: 1.5rem !important;
    height: auto !important;
    overflow-y: auto !important;
    // background: #f6f6f6 !important;
    -webkit-overflow-scrolling: touch !important;

    &_title {
      font-size: 1.4rem !important;
      margin-bottom: 1.5rem !important;
      padding-left: 0 !important;
    }

    &_step {
      display: flex !important;
      align-items: center !important;
      justify-content: space-between !important;
      padding: 1rem 0.5rem !important;

      &_item {
        display: flex !important;
        align-items: center !important;
        font-size: 1.2rem !important;
        flex-direction: column !important;

        &_dot {
          width: 2rem !important;
          height: 2rem !important;
          font-size: 1rem !important;
          margin-bottom: 0.5rem !important;
          margin-right: 0 !important;
        }

        &_img {
          width: 2rem !important;
          height: 2rem !important;
          margin-bottom: 0.5rem !important;
          margin-right: 0 !important;
        }
      }

      &_line {
        width: 20% !important;
        max-width: 5rem !important;
      }
    }

    &_step {
      height: auto !important;
      padding: 1rem !important;
      margin-bottom: 1rem !important;
      border-radius: 1rem !important;

      &_item {
        font-size: 1.4rem !important;

        &_dot {
          width: 2.5rem !important;
          height: 2.5rem !important;
          font-size: 1.2rem !important;
        }
      }

      &_line {
        width: 20% !important;
      }
    }

    &_box {
      flex-direction: column !important;

      &_cartList,
      &_container {
        width: 100% !important;
        padding: 1.5rem !important;
        border-radius: 1rem !important;
        margin-bottom: 1rem !important;
      }

      /* 购物车第一步的移动端样式 */
      &_cartList {
        /* 隐藏表头 */
        .cart_view_box_thead {
          display: none !important;

          .el-checkbox {
            .el-checkbox__input.is-checked .el-checkbox__inner {
              background-color: #6e4aeb !important;
              border-color: #6e4aeb !important;
            }
          }
        }

        /* 购物车商品行样式 */
        .cart_view_box_tr {
          display: flex !important;
          align-items: center !important;
          padding: 1rem 0 !important;
          border-bottom: 1px solid #f0f0f0 !important;
          margin-bottom: 0.8rem !important;
          position: relative !important;
          justify-content: space-between !important; /* 确保元素分布在两端 */
          gap: 0.8rem !important; /* 添加间距，防止元素相互挤压 */

          /* 隐藏宽度类，在移动端不需要 */
          .w117,
          .w377,
          .w114,
          .w147,
          .w1 {
            width: auto !important;
          }

          /* 复选框区域 */
          &_td:first-child {
            padding-left: 0 !important;
            padding-right: 1rem !important;

            .el-checkbox {
              transform: scale(1.2) !important;

              .el-checkbox__input.is-checked .el-checkbox__inner {
                background-color: #6e4aeb !important;
                border-color: #6e4aeb !important;
              }
            }
          }

          /* 商品信息区域 */
          &_td:nth-child(2) {
            display: flex !important;
            align-items: center !important;
            padding-right: 0 !important; /* 移除右内边距，避免与删除按钮重叠 */
            width: 100% !important; /* 确保占用全部可用宽度 */
            max-width: calc(
              100% - 6rem
            ) !important; /* 为删除按钮和复选框留出足够空间 */

            img {
              width: 8rem !important;
              height: 8rem !important;
              object-fit: contain !important;
              border-radius: 0.5rem !important;
              margin-right: 1rem !important;
              flex-shrink: 0 !important; /* 防止图片被压缩 */
            }

            /* 移除伪元素，在HTML中添加一个内容区域 */
            &::after {
              display: none !important;
            }
          }

          /* 新增：商品内容区域 */
          .product-info {
            display: flex !important;
            flex-direction: column !important;
            flex: 1 !important;
            min-width: 0 !important; /* 确保文本溢出时能够正常截断 */
            width: 100% !important; /* 铺满父容器 */

            &-name {
              font-size: 1.4rem !important;
              line-height: 1.4 !important;
              margin-bottom: 0.5rem !important;
              overflow: hidden !important;
              text-overflow: ellipsis !important;
              display: -webkit-box !important;
              -webkit-line-clamp: 2 !important;
              line-clamp: 2 !important; /* 标准属性，配合-webkit-前缀实现兼容性 */
              -webkit-box-orient: vertical !important;
              word-break: break-all !important; /* 允许在任意字符间断行 */
              width: 100% !important;
            }
            &-price {
              font-weight: 600 !important;
              color: #e4004f !important;
              font-size: 1.6rem !important;
              margin-bottom: 0.8rem !important;
            }

            &-quantity {
              display: flex !important;
              align-items: center !important;

              .el-input-number {
                width: 8.5rem !important;
                height: 2.8rem !important;
                border-radius: 0.4rem !important;
                border: 1px solid #dcdfe6 !important;
                display: flex !important;
                align-items: center !important;
                background-color: white !important;
                overflow: visible !important;

                .el-input-number__decrease,
                .el-input-number__increase {
                  background-color: #f5f5f5 !important;
                  border: none !important;
                  width: 2.8rem !important;
                  height: 2.8rem !important;
                  line-height: 2.8rem !important;
                  font-size: 1.4rem !important;
                  z-index: 1 !important;
                }

                .el-input__inner {
                  text-align: center !important;
                  font-size: 1.4rem !important;
                  height: 2.8rem !important;
                  line-height: 2.8rem !important;
                  padding: 0 2.8rem !important;
                  color: #333 !important;
                  background-color: white !important;
                  width: 100% !important;
                  border: none !important;
                  outline: none !important;
                  box-shadow: none !important;
                }

                .el-input {
                  width: 100% !important;
                  height: 100% !important;
                  line-height: 2.8rem !important;
                  flex: 1 !important;

                  .el-input__wrapper {
                    padding: 0 !important;
                    box-shadow: none !important;
                    height: 100% !important;
                    background-color: white !important;
                  }
                }
              }
            }
          }

          /* 删除按钮区域 */
          &_td:last-child {
            padding-right: 0.5rem !important;
            margin-left: auto !important; /* 确保它靠右对齐 */
            display: flex !important;
            align-items: center !important;
            justify-content: flex-end !important; /* 内容右对齐 */
            min-width: 4rem !important; /* 确保有足够的宽度 */
            flex-shrink: 0 !important; /* 防止被挤压 */

            &_action {
              color: #999 !important;
              font-size: 1.2rem !important;
              padding: 0.3rem 0.6rem !important;
              border-radius: 2rem !important;
              background-color: #f5f5f5 !important;
              white-space: nowrap !important;
              text-align: center !important;
            }
          }

          /* 隐藏原来的数量和价格区域，我们已经将它们移动到商品信息中 */
          &_td:nth-child(3),
          &_td:nth-child(4) {
            display: none !important;
          }
        }
      }
      &_title {
        font-size: 1.6rem !important;
        margin-bottom: 1.5rem !important;
      }

      &_list {
        width: 100% !important;
        margin-top: 1rem !important;
        padding: 1.5rem !important;
        border-radius: 1rem !important;
        margin-left: 0 !important;
      }

      /* 地址样式 */
      &_address {
        border-radius: 0.8rem !important;
        padding: 1.2rem !important;
        margin-bottom: 1rem !important;

        &_user {
          font-size: 1.4rem !important;

          .default-tag {
            font-size: 1.2rem !important;
            padding: 0.1rem 0.4rem !important;
          }
        }

        &_detail {
          font-size: 1.2rem !important;
        }

        &_action {
          position: absolute !important;
          right: 1rem !important;

          &_icon {
            width: 1.6rem !important;
            height: 1.6rem !important;
          }
        }
      }

      /* 添加地址按钮 */
      &_add {
        padding: 1.2rem !important;
        font-size: 1.4rem !important;
        height: auto !important;

        &_img {
          width: 1.6rem !important;
          height: 1.6rem !important;
        }
      }

      /* 支付方式 */
      &_payment {
        display: flex !important;
        flex-wrap: wrap !important;
        gap: 1rem !important;

        &_item {
          width: calc(25% - 2rem) !important;
          height: 5rem !important;
          padding: 0rem !important;
          min-width: auto !important;

          &_icon {
            // max-height: 2.5rem !important;
            object-fit: cover !important;
          }
        }
      }

      /* 配送方式 */
      &_deliver {
        padding: 1.2rem !important;
        font-size: 1.4rem !important;
        margin-bottom: 1.5rem !important;

        &_icon {
          width: 2rem !important;
          height: 2rem !important;
        }
      }

      /* 订单备注 */
      textarea {
        height: 8rem !important;
        padding: 1rem !important;
        font-size: 1.4rem !important;
        margin-bottom: 1.5rem !important;
      }

      /* 商品列表 */
      &_list_item {
        display: flex !important;
        padding: 1rem 0 !important;
        margin-bottom: 1rem !important;
        border-bottom: 1px solid #f0f0f0 !important;

        &_img {
          width: 6rem !important;
          height: 6rem !important;
          margin-right: 1rem !important;
          border-radius: 0.5rem !important;
          object-fit: contain !important;
        }

        &_main {
          flex: 1 !important;
          display: flex !important;
          flex-direction: column !important;
          justify-content: space-between !important;

          &_name {
            font-size: 1.4rem !important;
            line-height: 1.3 !important;
            margin-bottom: 0.5rem !important;
            display: -webkit-box !important;
            -webkit-line-clamp: 2 !important;
            line-clamp: 2 !important;
            -webkit-box-orient: vertical !important;
            overflow: hidden !important;
          }

          &_info {
            font-size: 1.4rem !important;
            color: #333 !important;
            display: flex !important;
            justify-content: space-between !important;

            span {
              color: #999 !important;
              font-size: 1.2rem !important;
            }
          }
        }
      }

      /* 底部按钮和价格 */
      &_all {
        font-size: 1.6rem !important;
        margin: 1rem 0 !important;
        color: #333 !important;
        font-weight: 600 !important;

        span {
          font-size: 1.8rem !important;
          color: #e4004f !important;
        }
      }

      /* 移动端全选区域 */
      &_mobile_select {
        display: flex !important;
        justify-content: space-between !important;
        align-items: center !important;
        padding: 1rem 0 !important;
        margin-bottom: 1rem !important;

        .mobile-check {
          display: flex !important;
          align-items: center !important;
          font-size: 1.4rem !important;

          .el-checkbox {
            .el-checkbox__input.is-checked .el-checkbox__inner {
              background-color: #6e4aeb !important;
              border-color: #6e4aeb !important;
            }
          }
        }

        &_count {
          font-size: 1.2rem !important;
          color: #666 !important;
        }
      }

      &_btn {
        width: 100% !important;
        height: 4.5rem !important;
        font-size: 1.6rem !important;
        border-radius: 2.5rem !important;
        background: #6e4aeb !important;
        color: white !important;
        display: flex !important;
        align-items: center !important;
        justify-content: center !important;
      }

      /* 订单摘要信息 */
      &_info {
        font-size: 1.4rem !important;
        margin: 0.5rem 0 !important;
        display: flex !important;
        justify-content: space-between !important;
        color: #666 !important;

        span {
          font-weight: 500 !important;
          color: #333 !important;
        }
      }

      &_titles {
        font-size: 1.5rem !important;
        margin-bottom: 1rem !important;
        font-weight: 600 !important;

        &_num {
          color: #fff !important;
        }
      }

      &_line {
        width: 100% !important;
        margin-bottom: 1rem !important;
        border-bottom: 1px solid #eee !important;
      }

      &_lines {
        width: 100% !important;
        margin: 1rem 0 !important;
        border-bottom: 1px solid #eee !important;
      }
    }

    /* 支付页面 */
    &_pay {
      padding: 1.5rem !important;

      &_main {
        width: 100% !important;
        padding: 1.5rem !important;
        border-radius: 1rem !important;
      }
    }
  }

  /* 下载页面样式调整 */
  .download_box {
    .banner-box_text {
      &_title {
        font-size: 2.6rem !important;
        line-height: 1.3 !important;
      }

      &_desc {
        font-size: 1.6rem !important;
        line-height: 1.5 !important;
      }
    }

    &_info {
      &_name {
        font-size: 2.4rem !important;
      }

      &_desc {
        font-size: 1.6rem !important;
        line-height: 1.5 !important;
      }

      &_tip {
        font-size: 1.4rem !important;
      }

      &_list_item_main_label {
        &_title {
          font-size: 1.2rem !important;
        }

        &_desc {
          font-size: 1.8rem !important;
        }
      }
    }
  }

  /* 移动端地址弹框样式 */
  .el-dialog.mobile-dialog {
    margin: 0 !important;
    position: absolute !important;
    left: 0 !important;
    bottom: 0 !important;
    width: 100% !important;
    border-radius: 1.5rem 1.5rem 0 0 !important;
    max-width: 100% !important;
    max-height: 90vh !important;
    overflow-y: auto !important;
    -webkit-overflow-scrolling: touch !important;

    .el-dialog__header {
      padding: 1.5rem !important;
      text-align: center !important;
      margin-right: 0 !important;
      border-bottom: 1px solid #f0f0f0 !important;
      position: relative !important;

      .el-dialog__title {
        font-size: 1.8rem !important;
        font-weight: 500 !important;
      }

      .el-dialog__headerbtn {
        position: absolute !important;
        top: -1.5rem !important;
        right: -1.5rem !important;
        font-size: 1.8rem !important;
      }
    }

    .el-dialog__body {
      max-height: 70vh !important;
      overflow-y: auto !important;
      -webkit-overflow-scrolling: touch !important;
    }

    .el-dialog__footer {
      padding: 1rem 1.5rem 2rem !important;
      border-top: 1px solid #f0f0f0 !important;

      .dialog-footer {
        display: flex !important;
        justify-content: space-between !important;
        gap: 1rem !important;

        button {
          flex: 1 !important;
          height: 4.5rem !important;
          font-size: 1.6rem !important;
          border-radius: 2.5rem !important;

          &:last-child {
            background: #6e4aeb !important;
            color: white !important;
            border-color: #6e4aeb !important;
          }
        }
      }
    }
  }

  /* 地址表单移动端适配 */
  .address-form {
    .form-row {
      margin-bottom: 1.5rem !important;

      &.form-row-triple {
        flex-direction: column !important;

        .form-select {
          width: 100% !important;
          margin-bottom: 1.5rem !important;

          &:last-child {
            margin-bottom: 0 !important;
          }
        }
      }
    }

    .form-input,
    :deep(.el-input__wrapper),
    :deep(.el-select__wrapper) {
      height: 5rem !important;
      font-size: 1.6rem !important;
      padding: 0.5rem 1.2rem !important;
    }

    :deep(.el-textarea__inner) {
      height: 5rem !important;
      line-height: 5rem !important;
      font-size: 1.6rem !important;
    }

    /* 姓名和电话输入框特殊样式 */
    .form-row:first-child .form-input,
    .form-row:nth-child(2) .form-input {
      width: 100% !important;
    }
  }
}

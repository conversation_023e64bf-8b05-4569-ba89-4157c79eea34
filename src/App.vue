<template>
  <header
    class="header"
    :class="{
      'header-mobile': deviceStore.isMobile,
      'header-tablet': deviceStore.isTablet,
    }"
  >
    <div class="header_view">
      <img
        v-if="!deviceStore.isMobile"
        src="@assets/logo.png"
        class="header_view_logo"
      />
      <img v-else src="@assets/<EMAIL>" class="header_view_logo" />

      <!-- 导航菜单 -->
      <div
        class="header_view_nav"
        :class="{ 'mobile-nav-open': mobileMenuOpen && deviceStore.isMobile }"
      >
        <!-- 移动端币种和语言切换 (右上角) -->
        <div v-if="deviceStore.isMobile" class="mobile-settings-header">
          <div class="mobile-settings-row">
            <!-- 币种切换 -->
            <el-dropdown
              trigger="click"
              @command="onChange"
              class="mobile-dropdown"
            >
              <span class="mobile-dropdown-trigger">
                {{ unit }}
                <el-icon class="el-icon--right">
                  <ArrowDown />
                </el-icon>
              </span>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item
                    v-for="item in unitList"
                    :key="item.id"
                    :command="item.code"
                  >
                    <span
                      class="currency-symbol"
                      v-if="item.code == 'CNY'"
                      style="margin-right: 0.9rem"
                    >
                      {{ item.symbol_left }}
                    </span>
                    <span class="currency-symbol" v-else
                      >{{ item.symbol_left }}
                    </span>
                    {{ item.code }}
                  </el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>

            <!-- 语言切换 -->
            <el-dropdown
              trigger="click"
              @command="onChangeLang"
              class="mobile-dropdown"
            >
              <span class="mobile-dropdown-trigger">
                {{ getCurrentLanguageName() }}
                <el-icon class="el-icon--right">
                  <ArrowDown />
                </el-icon>
              </span>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item
                    v-for="item in langList"
                    :key="item.id"
                    :command="item.name"
                  >
                    {{ item.name }}
                  </el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </div>
        </div>

        <div
          class="header_view_nav_item"
          :class="{ 'active-nav-item': isCurrentRoute('/') }"
          @click="onGoUrl('/')"
        >
          {{ t('app.home') }}
        </div>
        <div
          class="header_view_nav_item"
          :class="{ 'active-nav-item': isCurrentRoute('/download') }"
          @click="onGoUrl('/download')"
        >
          {{ t('app.app') }}
        </div>
        <div
          class="header_view_nav_item"
          :class="{ 'active-nav-item': isCurrentRoute('/goods') }"
          @click="onGoUrl('/goods')"
        >
          {{ t('app.product') }}
        </div>
        <div
          class="header_view_nav_item"
          :class="{ 'active-nav-item': isCurrentRoute('/wish') }"
          @click="onGoUrl('/wish')"
        >
          {{ t('app.like') }}
        </div>
      </div>
      <!-- 搜索框 -->
      <div class="search-box-container" v-if="showSearchBox">
        <div class="search-box">
          <div class="search-input-wrapper">
            <el-input
              v-model="searchKeyword"
              :placeholder="t('shu_ru_jin_hang_cha_xun')"
              clearable
              class="search-input"
              @keyup.enter="handleSearch"
            >
              <template #prefix>
                <img src="@assets/search.png" class="search-icon" />
              </template>
            </el-input>
          </div>
          <img
            src="@assets/<EMAIL>"
            class="close-search-btn"
            @click="toggleSearchBox"
          />
        </div>
      </div>
      <div
        class="header_view_action"
        :class="{ 'mobile-actions': deviceStore.isMobile }"
      >
        <!-- 搜索按钮 - 移动端显示 -->
        <!-- <div
          class="header_view_action_item"
          v-if="deviceStore.isMobile"
          @click="toggleSearchBox"
        >
          <img src="@assets/search.png" class="header_view_action_item_icon" />
        </div> -->

        <!-- 用户按钮 -->
        <div class="header_view_action_item">
          <img
            src="@assets/user.png"
            class="header_view_action_item_icon"
            @click="onGoUrl(deviceStore.isMobile ? '/center' : '/user')"
          />
        </div>

        <!-- 购物车按钮 -->
        <div
          class="header_view_action_item cart-icon-container"
          @click="drawer = true"
        >
          <img src="@assets/cart.png" class="header_view_action_item_icon" />
          <span v-if="cartCount > 0" class="cart-count">{{ cartCount }}</span>
        </div>

        <!-- 移动端菜单按钮 -->
        <div
          class="header_view_action_item"
          v-if="deviceStore.isMobile"
          @click="toggleMobileMenu"
        >
          <img
            v-if="!mobileMenuOpen"
            src="@assets/h5/<EMAIL>"
            class="header_view_action_item_icon"
          />
          <img
            v-else
            src="@assets/close.png"
            class="header_view_action_item_icon"
          />
        </div>

        <!-- 桌面端才显示的操作项 -->
        <template v-if="!deviceStore.isMobile">
          <div class="header_view_action_line"></div>
          <el-dropdown
            class="header_view_action_item"
            trigger="click"
            @command="onChange"
          >
            <span class="el-dropdown-link">
              {{ unit }}
              <el-icon class="el-icon--right">
                <ArrowDown />
              </el-icon>
            </span>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item
                  v-for="item in unitList"
                  :key="item.id"
                  :command="item.code"
                >
                  <span
                    class="currency-symbol"
                    v-if="item.code == 'CNY'"
                    style="margin-right: 0.9rem"
                  >
                    {{ item.symbol_left }}
                  </span>
                  <span class="currency-symbol" v-else
                    >{{ item.symbol_left }}
                  </span>
                  {{ item.code }}
                </el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
          <el-dropdown
            class="header_view_action_item"
            trigger="click"
            @command="onChangeLang"
          >
            <span class="el-dropdown-link">
              {{ getCurrentLanguageName() }}
              <el-icon class="el-icon--right">
                <ArrowDown />
              </el-icon>
            </span>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item
                  v-for="item in langList"
                  :key="item.id"
                  :command="item.name"
                >
                  {{ item.name }}
                </el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </template>
      </div>
    </div>
  </header>
  <div class="router-view-container">
    <div v-if="isPageLoading" class="page-loading-container">
      <div class="loading-spinner"></div>
    </div>
    <router-view v-slot="{ Component }">
      <transition name="fade" mode="out-in">
        <component :is="Component" @mounted="pageLoaded" />
      </transition>
    </router-view>
  </div>
  <footer
    class="footer"
    :class="{ 'footer-mobile': deviceStore.isMobile }"
    v-show="!isPageLoading"
  >
    <div class="footer_main">
      <h4
        class="footer_main_title"
        :style="deviceStore.isMobile ? 'font-size:2.2rem' : ''"
      >
        {{ t('lian_xi_wo_men') }}
      </h4>
      <div
        class="footer_main_tip"
        :style="deviceStore.isMobile ? 'font-size:1.4rem' : ''"
      >
        {{ t('ke_yi_zai_wo_men_de_bo_ke_cha_kan_gong_gao') }}
      </div>
      <div
        class="footer_main_link"
        v-if="contactInfo && contactInfo.whatsapp"
        @click="openLink(contactInfo.whatsapp)"
      >
        WhatsApp：<span>{{ contactInfo.whatsapp }}</span>
      </div>
      <div
        class="footer_main_concat"
        :style="deviceStore.isMobile ? 'flex-wrap: wrap' : ''"
      >
        <div
          class="footer_main_concat_f"
          :style="deviceStore.isMobile ? 'margin-right:0' : ''"
        >
          <img src="@assets/f.png" class="footer_main_concat_f_icon" />
        </div>
        <img
          src="@assets/x.png"
          class="footer_main_concat_item"
          :style="deviceStore.isMobile ? 'margin-right:0' : ''"
        />
        <img
          src="@assets/i.png"
          class="footer_main_concat_item"
          :style="deviceStore.isMobile ? 'margin-right:0' : ''"
        />
        <img
          src="@assets/youtube.png"
          class="footer_main_concat_item"
          :style="deviceStore.isMobile ? 'margin-right:0' : ''"
        />
        <img
          src="@assets/douyin.png"
          class="footer_main_concat_item"
          :style="deviceStore.isMobile ? 'margin-right:0' : ''"
        />
      </div>
    </div>
    <div class="footer_info">
      <div
        class="footer_info_title"
        :style="deviceStore.isMobile ? 'font-size:2.2rem' : ''"
      >
        {{ t('ding_yue_wo_men_de_shi_shi_tong_xun') }}
      </div>
      <div
        class="footer_info_desc"
        :style="deviceStore.isMobile ? 'font-size:1.4rem' : ''"
      >
        {{
          t(
            'zhi_chi_xin_bi_zhong_bo_ke_geng_xin_he_du_jia_you_hui_zhi_jie_fa_song_dao_nin_de_shou_jian_xiang'
          )
        }}
      </div>
      <div class="footer_info_action">
        <input
          v-model="subscribeEmail"
          class="footer_info_action_input"
          :placeholder="t('qing_shu_ru_nin_de_dian_zi_you_xiang')"
        />
        <div class="footer_info_action_btn" @click="onBind">
          {{ t('ding_yue_xin_wen') }}
          <img src="@assets/arrow.png" class="footer_info_action_btn_icon" />
        </div>
      </div>
      <div class="footer_info_tip">
        {{
          t(
            'nin_de_dian_zi_you_jian_di_zhi_jin_yong_yu_xiang_nin_fa_song_wo_men_de_xin_wen_tong_xun_zui_xin_zi_xun_he_you_hui_xin_xi_nin_ke_yi_sui_shi_tong_guo_xin_wen_tong_xun_zhong_de_lian_jie_qu_xiao_ding_yue_le_jie_geng_duo_guan_yu_wo_men_ru_he_guan_li_nin_de_shu_ju_he_nin_de_quan_li'
          )
        }}
      </div>
    </div>
  </footer>
  <el-drawer
    v-model="drawer"
    :show-close="false"
    title=""
    :direction="direction"
    :width="deviceStore.isMobile ? '100%' : '465px'"
  >
    <template #header>
      <div class="drawer_header">
        <div class="drawer_header_main">
          {{ t('cart.cart') }}
          <span></span>
          <img
            class="drawer_header_icon"
            src="@assets/close.png"
            @click="drawer = false"
          />
        </div>
      </div>
    </template>
    <div class="isEmpty" v-if="isEmpty">
      <img src="@assets/cart_none.png" class="isEmpty_img" />
      <div class="isEmpty_text">{{ t('gou_wu_che_li_shi_mo_ye_mei_you') }}</div>
      <div class="isEmpty_subtext">
        {{ t('gan_kuai_qu_shang_dian_li_mian_guang_guang_ba') }}
      </div>
      <div
        class="isEmpty_action"
        @click="
          drawer = false;
          onGoUrl('/goods');
        "
      >
        {{ t('qu_guang_guang') }}
      </div>
    </div>
    <div v-else>
      <div
        v-if="loading"
        class="loading-state"
        v-loading="loading"
        style="background-color: #fff; padding-top: 10rem"
      ></div>
      <div class="cart_view" v-else>
        <div
          class="cart_view_item"
          v-for="item in cartList"
          :key="item.cart_id"
        >
          <el-checkbox
            :model-value="item.selected == 1"
            @change="(checked) => handleSelectItem(item.cart_id, checked)"
            class="cart_view_item_checkbox"
          ></el-checkbox>
          <img :src="item.image_url" class="cart_view_item_img" />
          <div class="cart_view_item_main">
            <p class="cart_view_item_name">{{ item.name_format }}</p>
            <span class="cart_view_item_price">{{ item.price_format }}</span>
            <el-input-number
              :model-value="item.quantity"
              :min="1"
              :max="item.stock"
              @change="(value) => handleUpdateQuantity(item.cart_id, value)"
              :step="1"
              size="small"
            />
            <img
              src="@assets/trash.png"
              class="cart_view_item_del"
              @click="handleRemoveItem(item.cart_id)"
            />
          </div>
        </div>
      </div>
    </div>
    <template #footer v-if="!isEmpty">
      <div class="cart_view_footer">
        <el-checkbox
          :model-value="selectAll"
          @change="handleSelectAll"
          class="cart_view_footer_checkbox"
          >{{ t('cart.chooseAll') }} ({{ selectedCount }})</el-checkbox
        >
        <div class="cart_view_footer_total">{{ totalAmount }}</div>
        <div
          class="cart_view_footer_submit"
          @click="handleCheckout"
          :class="{ disabled: selectedCount === 0 }"
          :style="deviceStore.isMobile ? 'margin-bottom: 0.6rem' : ''"
        >
          {{ t('cart.toPayment') }}
        </div>
        <div class="cart_view_footer_cart" @click="handleViewCart">
          {{ t('cart.action') }}
        </div>
      </div>
    </template>
  </el-drawer>
</template>
<script setup>
import { useRouter } from 'vue-router';
import { useI18n } from 'vue-i18n';
import {
  ref,
  onMounted,
  onUnmounted,
  computed,
  watch,
  nextTick,
  provide,
} from 'vue';
import { ElMessage } from 'element-plus';
import { ArrowDown, ShoppingCart } from '@element-plus/icons-vue';
import { useDeviceStore } from '@/stores/device';
import {
  GetCartList,
  SelectCartItems,
  UnselectCartItems,
  UpdateCart,
  RemoveCartItem,
} from '@api/cart';
import { GetCheckoutInfo } from '@api/checkout';
import {
  setLanguage,
  getLanguage,
  setCurrency,
  getCurrency,
} from '@utils/storage';
import {
  SUPPORTED_LANGUAGES,
  getLanguageName,
  convertToApiLanguage,
  convertFromApiLanguage,
  isValidLanguage,
} from '@utils/languageInit';
import {
  cartUpdated,
  currencyUpdated,
  triggerCurrencyUpdate,
} from '@utils/eventBus'; // 导入更新通知
const { t, locale } = useI18n();
import {
  GetHomeInfo,
  GetSupportedLanguages,
  GetSupportedCurrencies,
  SetCurrency,
  SetLanguage,
} from '@api';

const router = useRouter();
const deviceStore = useDeviceStore();
const unit = ref('USD');
const unitList = ref([]);

// 页面加载状态控制
const isPageLoading = ref(true);
provide('isPageLoading', isPageLoading); // 提供给子组件使用

// 页面加载完成回调
const pageLoaded = () => {
  // 使用短延迟确保所有内容都已经准备好
  setTimeout(() => {
    isPageLoading.value = false;
  }, 300);
};

// 移动端相关状态
const mobileMenuOpen = ref(false);

// 使用统一的语言列表
const langList = ref(SUPPORTED_LANGUAGES);

const subscribeEmail = ref(''); // 添加邮箱输入框的响应式变量

const onBind = () => {
  if (!subscribeEmail.value || !subscribeEmail.value.trim()) {
    ElMessage.warning(t('qing_shu_ru_nin_de_dian_zi_you_xiang'));
    return;
  }

  // 简单的邮箱格式验证
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  if (!emailRegex.test(subscribeEmail.value)) {
    ElMessage.warning(t('qing_shu_ru_you_xiao_de_dian_zi_you_xiang'));
    return;
  }

  ElMessage.success(t('ding_yue_cheng_gong'));
  subscribeEmail.value = ''; // 清空输入框
};

const openLink = (account) => {
  // 打开WhatsApp 链接
  const whatsappUrl = `https://wa.me/${account}`;
  window.open(whatsappUrl, '_blank');
};

const onChangeLang = async (lang) => {
  try {
    // 获取语言对应的locale代码
    const langItem = langList.value.find((item) => item.name === lang);
    if (langItem) {
      // 准备API调用的语言代码
      const apiLang = convertToApiLanguage(langItem.lang);

      // 调用接口设置语言
      // await SetLanguage(apiLang);
      console.log('语言已设置为:', apiLang);

      // 更新本地UI和存储
      locale.value = langItem.lang; // 使用语言代码而不是名称
      setLanguage(langItem.lang);

      console.log(
        '用户手动切换语言为:',
        langItem.name,
        '(',
        langItem.lang,
        ')'
      );
    }
  } catch (error) {
    console.error('设置语言失败:', error);
  }
};

const searchKeyword = ref('');
const showSearchBox = ref(false);
const drawer = ref(false);
const direction = ref('rtl');

// 购物车相关数据
const cartList = ref([]);
const loading = ref(false);
const selectAll = ref(false);

// 计算属性
const isEmpty = computed(() => cartList.value.length === 0);

const selectedItems = computed(() => {
  return cartList.value.filter((item) => item.selected);
});

const selectedCount = computed(() => {
  return selectedItems.value.length;
});

// 计算购物车中的商品总数
const cartCount = computed(() => {
  return cartList.value.length;
});

// 结算信息和总价
const checkoutInfo = ref(null);
const totalAmount = computed(() => {
  // 如果有结算信息，使用API返回的总价
  if (checkoutInfo.value && checkoutInfo.value.totals) {
    const subTotalItem = checkoutInfo.value.totals.find(
      (item) => item.code === 'sub_total'
    );
    if (subTotalItem) {
      return subTotalItem.amount_format;
    }
  }

  // 作为备选方案，计算本地总价
  const total = selectedItems.value.reduce((sum, item) => {
    return sum + item.price * item.quantity;
  }, 0);
  return `$${total.toFixed(2)}`;
});

const contactInfo = ref({});

// 监听选中状态变化，更新全选状态
watch(
  cartList,
  () => {
    selectAll.value =
      cartList.value.length > 0 &&
      cartList.value.every((item) => item.selected);
  },
  { deep: true }
);

// 监听抽屉打开，加载购物车数据
watch(drawer, (isOpen) => {
  if (isOpen) {
    loadCartList();
  }
});

// 组件卸载时移除事件监听
onUnmounted(() => {
  document.removeEventListener('click', handleOutsideClick);
  deviceStore.cleanup(); // 清理设备检测相关的事件监听
});

// 监听购物车更新事件
watch(cartUpdated, () => {
  loadCartList();
});

// 移动端菜单切换
const toggleMobileMenu = () => {
  mobileMenuOpen.value = !mobileMenuOpen.value;
};

// 点击导航项时关闭移动端菜单
const onGoUrl = (url) => {
  router.push(url);
  mobileMenuOpen.value = false; // 导航后关闭菜单
};

// getBrowserLanguage 函数已移动到 languageInit.js 中

// 初始化组件时检查语言设置和加载购物车数据
onMounted(() => {
  // 初始化设备检测
  deviceStore.initDevice();

  // 语言已经在 main.js 中初始化，这里只需要确保 locale 与当前设置一致
  const currentLanguage = getLanguage();
  if (currentLanguage && isValidLanguage(currentLanguage)) {
    locale.value = currentLanguage;
    console.log('App.vue 中确认当前语言设置:', currentLanguage);
  }

  // 从本地存储获取保存的币种
  const savedCurrency = getCurrency();
  if (savedCurrency) {
    unit.value = savedCurrency;
  }

  // 加载支持的语言列表
  loadSupportedLanguages();

  // 加载支持的币种列表
  loadSupportedCurrencies();

  // 加载购物车数据
  loadCartList();
  loadInfo();
});

// 加载支持的语言列表
const loadSupportedLanguages = async () => {
  try {
    const response = await GetSupportedLanguages();
    console.log('获取服务器语言列表', response);

    // 如果服务器返回了当前语言设置，且与本地不一致，则更新本地设置
    // if (response && response.current) {
    //   const serverLang = convertFromApiLanguage(response.current.code);
    //   const localLang = getLanguage();

    //   if (serverLang !== localLang && isValidLanguage(serverLang)) {
    //     console.log('服务器语言设置与本地不一致，更新为:', serverLang);
    //     locale.value = serverLang;
    //     setLanguage(serverLang);
    //   }
    // }

    // 可选：如果服务器返回了支持的语言列表，可以与本地列表进行对比
    // 这里暂时使用本地定义的语言列表
  } catch (error) {
    console.error('获取语言列表失败:', error);
    // 失败时继续使用本地语言设置，不影响用户体验
  }
};

// 加载支持的币种列表
const loadSupportedCurrencies = async () => {
  try {
    const response = await GetSupportedCurrencies();
    console.log('获取币种列表', response);
    if (response && response.list) {
      unitList.value = response.list.map((item) => ({
        ...item,
      }));
    }
    if (response && response.current) {
      unit.value = response.current;
      setCurrency(response.current);
    }
  } catch (error) {
    console.error('获取币种列表失败:', error);
  }
};

// 监听路由变化，重新获取购物车数据
watch(
  () => router.currentRoute.value.path,
  () => {
    loadCartList();
  }
);

const loadInfo = () => {
  GetHomeInfo()
    .then((res) => {
      if (res) {
        contactInfo.value = res.content;
      }
    })
    .catch((error) => {
      console.error('获取数据失败:', error);
    });
};

// 加载结算信息
const loadCheckoutInfo = async () => {
  try {
    const response = await GetCheckoutInfo();
    console.log('结算信息:', response);
    checkoutInfo.value = response;
    return response;
  } catch (error) {
    console.error('获取结算信息失败:', error);
    checkoutInfo.value = null;
  }
};

// 获取购物车列表
const loadCartList = async () => {
  try {
    loading.value = true;
    const response = await GetCartList();

    if (response && response.carts) {
      cartList.value = response.carts.map((item) => ({
        cart_id: item.cart_id,
        name_format: item.name_format,
        image_url: item.image_url,
        price: item.price,
        price_format: item.price_format,
        quantity: item.quantity,
        stock: item.stock,
        selected: item.selected || false,
        sku_id: item.sku_id,
      }));

      // 如果有选中的商品，更新结算信息
      if (cartList.value.some((item) => item.selected)) {
        await loadCheckoutInfo();
      }
    }
  } catch (error) {
    console.error('获取购物车列表失败:', error);
    // ElMessage.error('获取购物车列表失败');
  } finally {
    loading.value = false;
  }
};

// 全选/取消全选
const handleSelectAll = async (checked) => {
  try {
    const cartIds = cartList.value.map((item) => item.cart_id);

    // 先在本地更新选中状态，提供即时反馈
    cartList.value.forEach((item) => (item.selected = checked));

    // 异步调用API更新服务器数据
    if (checked) {
      await SelectCartItems(cartIds);
    } else {
      await UnselectCartItems(cartIds);
    }

    // 更新结算信息
    if (checked) {
      await loadCheckoutInfo();
    } else {
      checkoutInfo.value = null;
    }
  } catch (error) {
    console.error('批量选择失败:', error);
    // ElMessage.error('操作失败');

    // 操作失败时回滚状态
    cartList.value.forEach((item) => (item.selected = !checked));
    selectAll.value = !checked;
  }
};

// 选中/取消选中单个商品
const handleSelectItem = async (cartId, checked) => {
  try {
    // 先在本地更新选中状态，提供即时反馈
    const item = cartList.value.find((item) => item.cart_id === cartId);
    if (item) {
      item.selected = checked;
    }

    // 异步调用API更新服务器数据
    if (checked) {
      await SelectCartItems([cartId]);
    } else {
      await UnselectCartItems([cartId]);
    }

    // 更新结算信息
    if (cartList.value.some((item) => item.selected)) {
      await loadCheckoutInfo();
    } else {
      checkoutInfo.value = null;
    }
  } catch (error) {
    console.error('选择商品失败:', error);
    // ElMessage.error('操作失败');

    // 操作失败时回滚状态
    const item = cartList.value.find((item) => item.cart_id === cartId);
    if (item) {
      item.selected = !checked;
    }
  }
};

// 更新商品数量
const handleUpdateQuantity = async (cartId, quantity) => {
  try {
    const item = cartList.value.find((item) => item.cart_id === cartId);
    if (!item) return;

    // 先在本地更新数量，提供即时反馈
    item.quantity = quantity;

    // 异步调用API更新服务器数据
    await UpdateCart(cartId, {
      quantity: quantity,
      sku_id: item.sku_id,
    });

    // 如果是选中的商品，更新结算信息
    if (item.selected && cartList.value.some((item) => item.selected)) {
      await loadCheckoutInfo();
    }

    // 成功时轻提示
    // ElMessage.success('数量更新成功');
  } catch (error) {
    console.error('更新数量失败:', error);
    // ElMessage.error('更新数量失败');

    // 只有在发生错误时才重新加载购物车列表
    loadCartList();
  }
};

// 删除商品
const handleRemoveItem = async (cartId) => {
  try {
    // 先在本地删除商品，提供即时反馈
    cartList.value = cartList.value.filter((item) => item.cart_id !== cartId);

    // 异步调用API更新服务器数据
    await RemoveCartItem(cartId);

    ElMessage.success(t('shang_pin_yi_shan_chu'));
  } catch (error) {
    console.error('删除商品失败:', error);
    // ElMessage.error('删除商品失败');

    // 删除失败时重新加载购物车
    loadCartList();
  }
};

// 查看购物车 - 跳转到第一步
const handleViewCart = () => {
  drawer.value = false;
  router.push({ path: '/confirm', query: { step: 1 } });
};

// 去结算 - 跳转到第二步
const handleCheckout = () => {
  if (selectedCount.value === 0) {
    ElMessage.warning(t('qing_xuan_ze_shang_pin'));
    return;
  }
  drawer.value = false;
  router.push({ path: '/confirm', query: { step: 2 } });
};

const toggleSearchBox = () => {
  showSearchBox.value = !showSearchBox.value;
  if (showSearchBox.value) {
    // 当搜索框显示时，聚焦输入框
    setTimeout(() => {
      document.querySelector('.search-input input')?.focus();
    }, 100);

    // 添加点击外部关闭事件
    setTimeout(() => {
      document.addEventListener('click', handleOutsideClick);
    }, 0);
  } else {
    // 移除点击外部关闭事件
    document.removeEventListener('click', handleOutsideClick);
  }
};

// 点击外部区域关闭搜索框
const handleOutsideClick = (event) => {
  const searchBox = document.querySelector('.search-box');
  const searchIcon = document.querySelector(
    '.header_view_action_item img[src*="search.png"]'
  );

  if (
    searchBox &&
    !searchBox.contains(event.target) &&
    searchIcon &&
    !searchIcon.contains(event.target)
  ) {
    showSearchBox.value = false;
    document.removeEventListener('click', handleOutsideClick);
  }
};

const handleSearch = () => {
  if (!searchKeyword.value.trim()) {
    ElMessage.warning(t('qing_shu_ru_sou_suo_guan_jian_ci'));
    return;
  }
  // 跳转到搜索结果页面，或在当前页面显示搜索结果
  router.push({
    path: '/goods',
    query: { keyword: searchKeyword.value },
  });
  showSearchBox.value = false; // 搜索后隐藏搜索框
};

const onChange = async (currency) => {
  try {
    // 调用接口设置币种
    // await SetCurrency(currency);

    // 更新本地UI和存储
    unit.value = currency;
    setCurrency(currency);

    // 切换币种之后发送通知，更新商品列表（首页、商品页和收藏页）
    triggerCurrencyUpdate();

    // // 刷新当前页面以确保所有价格正确更新
    window.location.reload();
  } catch (error) {
    console.error('设置币种失败:', error);
  }
};

// 判断当前路由是否匹配
const isCurrentRoute = (path) => {
  return router.currentRoute.value.path === path;
};

// 获取当前语言名称
const getCurrentLanguageName = () => {
  return getLanguageName(locale.value);
};
</script>
<style>
.el-drawer__header {
  padding: 0;
  box-sizing: border-box;
}

.el-drawer__body {
  padding: 0 2.4rem;
}

/* 强制移动设备上的抽屉宽度为100% */
@media screen and (max-width: 768px) {
  .el-drawer.rtl {
    width: 100% !important;
    max-width: 100% !important;
  }

  /* 确保抽屉内容区域也是100%宽度 */
  .el-drawer .el-drawer__body {
    width: 100% !important;
    box-sizing: border-box;
    padding: 0 1.5rem;
  }

  /* 确保内部所有元素不会导致水平滚动 */
  .cart_view {
    width: 100%;
    box-sizing: border-box;
  }

  /* 移动端购物车字体大小调整 */
  .cart_view_item_name {
    font-size: 1.4rem !important;
    height: auto !important;
    line-height: 1.4 !important;
  }

  .cart_view_item_price {
    font-size: 1.6rem !important;
    height: auto !important;
    line-height: 1.4 !important;
  }

  .cart_view_item {
    padding: 2rem 0 !important;
  }

  .cart_view_item_img {
    width: 8rem !important;
    height: 8rem !important;
  }

  .cart_view_item_main {
    padding-left: 1.2rem !important;
  }

  .cart_view_footer_total {
    font-size: 1.4rem !important;
  }

  .cart_view_footer_submit,
  .cart_view_footer_cart {
    font-size: 1.5rem !important;
  }

  .cart_view_footer {
    padding: 0 !important;
  }

  .drawer_header {
    width: 100%;
    padding: 0 1.5rem;
  }

  .drawer_header_main {
    font-size: 1.6rem !important;
  }

  /* 调整Element Plus组件在移动端的样式 */
  .el-checkbox {
    --el-checkbox-font-size: 1.4rem !important;
  }

  .el-checkbox__label {
    font-size: 1.4rem !important;
  }

  .cart_view_item .el-input-number {
    --el-input-number-width: 10rem !important;
    --el-input-height: 3rem !important;
    --el-font-size-base: 1.4rem !important;
  }

  .cart_view_item .el-input-number__decrease,
  .cart_view_item .el-input-number__increase {
    width: 2.5rem !important;
  }

  /* 全局移动端适配样式 */
  .el-drawer__body {
    padding: 0 1.5rem !important;
    overflow-x: hidden !important;
  }

  .el-drawer__header {
    margin-bottom: 0 !important;
  }

  /* 移动端全局样式调整 */
  .cart_view_footer {
    display: flex;
    flex-direction: column;
    width: 100%;
    gap: 1rem;
  }

  .cart_view_footer_checkbox {
    align-self: flex-start;
    margin-bottom: 1rem;
  }

  .cart_view_footer_total {
    align-self: flex-start;
    margin-bottom: 1rem;
  }

  /* 空购物车状态适配 */
  .isEmpty_text {
    font-size: 1.8rem !important;
  }

  .isEmpty_subtext {
    font-size: 1.4rem !important;
  }

  .isEmpty_action {
    font-size: 1.5rem !important;
    height: 4rem !important;
    width: 20rem !important;
  }

  .isEmpty_img {
    width: 15rem !important;
    height: 15rem !important;
  }
}

.loading-state {
  padding: 2rem;
  height: 60rem;
}

.cart_view_footer {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.cart_view_footer_total {
  color: #e74c3c;
  font-weight: 600;
  font-size: 1.6rem;
}

.cart_view_item_del:hover {
  opacity: 0.7;
  cursor: pointer;
}

.cart_view_footer_submit.disabled {
  background-color: #cccccc;
  cursor: not-allowed;
  opacity: 0.7;
}

.cart-icon-container {
  position: relative;
  display: inline-block;
}

.cart-count {
  position: absolute;
  top: -0.8rem;
  right: -0.8rem;
  background-color: #e74c3c;
  color: white;
  border-radius: 50%;
  min-width: 1.6rem;
  height: 1.6rem;
  font-size: 1.2rem;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 0.4rem;
}

.active-nav-item {
  color: #6e4aeb !important;
}

.mobile-settings-header {
  /* position: absolute;
  top: 1.5rem;
  right: 1.5rem;
  z-index: 100;
  width: auto; */
  background-color: #f6f6f6;
  width: 100%;
  height: 4rem;
  display: flex;
  align-items: center;
  justify-content: flex-end;
}

.mobile-settings-row {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  gap: 1.5rem;
}

.mobile-dropdown {
  margin-left: 0.5rem;
}

.mobile-dropdown-trigger {
  display: flex;
  align-items: center;
  padding: 0.4rem 0.8rem;
  font-size: 1.4rem;
  cursor: pointer;
  background: #f5f5f5;
  border-radius: 0.4rem;
}

/* 移动端导航栏固定定位 */
@media screen and (max-width: 768px) {
  .router-view-container {
    margin-top: 0 !important; /* 移除外边距 */
    padding-top: 6rem !important; /* 改用内边距，使其更加自然 */
  }

  .search-box-container {
    position: fixed !important;
    top: 6rem !important; /* 与移动端头部高度对应 */
    left: 0 !important;
    width: 100% !important;
    z-index: 99 !important;
    padding-top: 0.5rem !important; /* 添加一点顶部内边距使其与导航栏有一点间隔 */
    padding-bottom: 0.5rem !important;
    background-color: rgba(255, 255, 255, 0.95) !important; /* 半透明背景色 */
  }

  .search-box {
    width: 95% !important;
    margin: 0 auto !important; /* 居中显示，两边有一点空间 */
    box-shadow: 0 1px 6px rgba(0, 0, 0, 0.1) !important; /* 柔和的阴影 */
  }

  /* 修复Element Plus下拉菜单在移动端的显示问题 */
  .el-dropdown-menu {
    z-index: 3000 !important; /* 确保下拉菜单显示在导航栏之上 */
  }
}

.search-box-container {
  position: absolute;
  top: 100%;
  left: 0;
  width: 100%;
  z-index: 1000;
  display: flex;
  justify-content: flex-end;
  padding: 0;
}

.search-box {
  position: relative;
  width: 90%;
  max-width: 44rem;
  background-color: white;
  border-radius: 0.5rem;
  display: flex;
  align-items: center;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  margin-top: 0.5rem;
}

.search-input-wrapper {
  flex: 1;
  padding: 0.8rem;
}

.search-input {
  font-size: 1.6rem;
}

.search-input .el-input__wrapper {
  background-color: #f3f4f8;
  border: none;
}

.close-search-btn {
  width: 2.4rem;
  height: 2.4rem;
  margin-right: 1.5rem;
  margin-left: 0.8rem;
  cursor: pointer;
  transition: opacity 0.2s;
}

.close-search-btn:hover {
  opacity: 1;
}

.search-icon {
  width: 2.4rem;
  height: 2.4rem;
  margin-right: 0.5rem;
}

.search-input :deep(.el-input__wrapper) {
  padding: 1.2rem;
  box-shadow: none !important;
}

.search-input :deep(.el-input__inner) {
  height: 4rem;
  font-size: 1.6rem;
}

.close-icon {
  cursor: pointer;
  font-size: 1.6rem;
  color: #999;
}

.close-icon:hover {
  color: #333;
}

/* 调整头部的定位，确保搜索框可以正确定位 */
.header {
  position: sticky;
  position: -webkit-sticky; /* Safari支持 */
  top: 0;
  z-index: 100;
  width: 100%;
  background: #ffffff;
  -webkit-backface-visibility: hidden; /* 防止iOS中的闪烁 */
  backface-visibility: hidden;
}

/* 移动设备上的样式将在 mobile-fix.scss 中被覆盖为 fixed */
@media screen and (max-width: 768px) {
  .header {
    position: fixed;
  }

  /* 移除body的上边距 */
  body {
    padding-top: 0;
  }

  /* 调整路由视图容器的上边距，使其更加自然 */
  .router-view-container {
    margin-top: 0 !important; /* 移除路由容器的顶部边距 */
    padding-top: 6rem !important; /* 改用padding，视觉上更自然 */
  }
}

/* 加载状态样式 */
.page-loading-container {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #ffffff;
  z-index: 1000;
}

.loading-spinner {
  width: 50px;
  height: 50px;
  border: 5px solid rgba(0, 0, 0, 0.1);
  border-radius: 50%;
  border-top-color: #6e4aeb;
  animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

/* 页面过渡动画 */
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

.header_view {
  position: relative;
}
</style>
<style lang="scss" scoped>
@import url(./app.scss);
</style>

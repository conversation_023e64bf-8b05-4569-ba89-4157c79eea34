import { createApp } from 'vue';
import { createI18n } from 'vue-i18n';
import ElementPlus from 'element-plus';
import * as ElementPlusIconsVue from '@element-plus/icons-vue';
import 'element-plus/dist/index.css';
import './style.css';
import router from './router.js';
import { createPinia } from 'pinia';
import './rem.js';
import './mobile-fix.scss'; // 引入移动端样式修复文件
import App from './App.vue';
import en from './locales/en.json';
import zh from './locales/zh.json';
import Py from './locales/Py.json';
import de from './locales/de.json';
import es from './locales/es.json';
import fr from './locales/fr.json';
import id from './locales/id.json';
import it from './locales/it.json';
import ja from './locales/ja.json';
import ko from './locales/ko.json';
import hk from './locales/hk.json';
import { initializeLanguage, isFirstVisit } from './utils/languageInit';

// 初始化语言设置
// 首次访问时会自动检测浏览器语言，之后使用本地缓存
const initialLanguage = initializeLanguage();

// 记录是否为首次访问，用于调试
if (isFirstVisit()) {
  console.log('首次访问，已根据浏览器语言设置为:', initialLanguage);
} else {
  console.log('从本地缓存读取语言设置:', initialLanguage);
}

const i18n = createI18n({
  locale: initialLanguage, // 使用初始化后的语言
  fallbackLocale: 'en',
  legacy: false, // 使用 Composition API 模式
  globalInjection: true, // 全局注入 $t 函数
  messages: {
    en,
    zh,
    Py,
    de,
    es,
    fr,
    id,
    it,
    ja,
    ko,
    hk,
  },
});
const pinia = createPinia();
const app = createApp(App);
for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
  app.component(key, component);
}
app.use(router);
app.use(pinia);
app.use(ElementPlus);

app.use(i18n);
app.mount('#app');

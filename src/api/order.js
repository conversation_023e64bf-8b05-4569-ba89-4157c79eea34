import { get, post } from '@utils/request';

/**
 * 获取订单列表
 * @param {Object} params - 查询参数
 * @param {string} params.status - 订单状态 (可选)
 * @param {number} params.page - 页码 (可选，默认1)
 * @param {number} params.limit - 每页数量 (可选，默认10)
 * @returns {Promise}
 */
export const GetOrderList = (params = {}) => {
  return get('/v1/account/orders', params);
};

/**
 * 获取订单详情（公共接口）
 * @param {string} orderNumber - 订单号
 * @returns {Promise}
 */
export const GetOrderDetail = (orderNumber) => {
  return get(`/v1/orders/${orderNumber}`);
};

/**
 * 获取订单支付信息
 * @param {string} orderNumber - 订单号
 * @returns {Promise}
 */
export const GetOrderPayInfo = (orderNumber) => {
  return get(`/v1/orders/${orderNumber}/pay`);
};

/**
 * 取消订单
 * @param {string} orderNumber - 订单号
 * @returns {Promise}
 */
export const CancelOrder = (orderNumber) => {
  return post(`/v1/orders/${orderNumber}/cancel`);
};

/**
 * 完成订单
 * @param {string} orderNumber - 订单号
 * @returns {Promise}
 */
export const CompleteOrder = (orderNumber) => {
  return post(`/v1/orders/${orderNumber}/complete`);
};

export const GetOrderStats = () => {
  return get('/v1/orders/stats');
};

import { post } from '@utils/request';

/**
 * PayPal 创建支付
 * @param {Object} paymentData - 支付数据
 * @returns {Promise}
 */
export const CatchreatePayPalPayment = (paymentData = {}) => {
  return post('/paypal/create', paymentData);
};

/**
 * PayPal 捕获支付
 * @param {Object} captureData - 捕获数据
 * @returns {Promise}
 */
export const CapturePayPalPayment = (captureData = {}) => {
  return post('/paypal/capture', captureData);
};

/**
 * Stripe 创建支付
 * @param {Object} paymentData - 支付数据
 * @returns {Promise}
 */
export const CreateStripePayment = (paymentData = {}) => {
  return post('/stripe/create', paymentData);
};

/**
 * Stripe 捕获支付
 * @param {Object} captureData - 捕获数据
 * @returns {Promise}
 */
export const CaptureStripePayment = (captureData = {}) => {
  return post('/stripe/capture', captureData);
};

/**
 * BitPay 创建支付
 * @param {Object} paymentData - 支付数据
 * @returns {Promise}
 */
export const CreateBitPayPayment = (paymentData = {}) => {
  return post('/bitpay/create', paymentData);
};

/**
 * BitPay 捕获支付
 * @param {Object} captureData - 捕获数据
 * @returns {Promise}
 */
export const CaptureBitPayPayment = (captureData = {}) => {
  return post('/bitpay/capture', captureData);
};

/**
 * 微信H5 创建支付
 * @param {Object} paymentData - 支付数据
 * @returns {Promise}
 */
export const CreateWeChatH5Payment = (paymentData = {}) => {
  return post('/wechat_h5/create', paymentData);
};

/**
 * 微信H5 捕获支付
 * @param {Object} captureData - 捕获数据
 * @returns {Promise}
 */
export const CaptureWeChatH5Payment = (captureData = {}) => {
  return post('/wechat_h5/capture', captureData);
};

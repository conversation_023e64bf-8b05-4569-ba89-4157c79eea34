import { get, post, put, del } from '@utils/request';

/**
 * 获取地址列表
 * @returns {Promise}
 */
export const GetAddressList = () => {
  return get('/v1/account/addresses');
};

/**
 * 获取单个地址详情
 * @param {number} addressId - 地址ID
 * @returns {Promise}
 */
export const GetAddressDetail = (addressId) => {
  return get(`/v1/account/addresses/${addressId}`);
};

/**
 * 创建新地址
 * @param {Object} data - 地址信息
 * @param {string} data.name - 收货人姓名
 * @param {string} data.phone - 手机号
 * @param {number} data.country_id - 国家ID
 * @param {number} data.zone_id - 省份ID
 * @param {number} data.city_id - 城市ID
 * @param {string} data.city - 城市名称
 * @param {string} data.zipcode - 邮编
 * @param {string} data.address_1 - 详细地址1
 * @param {string} data.address_2 - 详细地址2
 * @param {boolean} data.default - 是否默认地址
 * @returns {Promise}
 */
export const CreateAddress = (data) => {
  return post('/v1/account/addresses', data);
};

/**
 * 更新地址
 * @param {number} addressId - 地址ID
 * @param {Object} data - 地址信息
 * @param {string} data.name - 收货人姓名
 * @param {string} data.phone - 手机号
 * @param {number} data.country_id - 国家ID
 * @param {number} data.zone_id - 省份ID
 * @param {number} data.city_id - 城市ID
 * @param {string} data.city - 城市名称
 * @param {string} data.zipcode - 邮编
 * @param {string} data.address_1 - 详细地址1
 * @param {string} data.address_2 - 详细地址2
 * @param {boolean} data.default - 是否默认地址
 * @returns {Promise}
 */
export const UpdateAddress = (addressId, data) => {
  return put(`/v1/account/addresses/${addressId}`, data);
};

/**
 * 删除地址
 * @param {number} addressId - 地址ID
 * @returns {Promise}
 */
export const DeleteAddress = (addressId) => {
  return del(`/v1/account/addresses/${addressId}`);
};

// 根据国家id 获取省份 /countries/40/zones
export const GetZones = (countryId) => {
  return get(`/countries/${countryId}/zones`);
};

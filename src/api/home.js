import { get, post } from '@utils/request';

/**
 * 获取首页信息
 * @returns {Promise}
 */
export const GetHomeInfo = () => {
  return get('/v1/index');
};

/**
 * 获取最新商品
 * @returns {Promise}
 */
export const GetLatestProducts = () => {
  return get('/v1/latest_products');
};

/**
 * 获取支持的语言列表
 * @returns {Promise}
 */
export const GetSupportedLanguages = () => {
  return get('/v1/languages');
};

/**
 * 获取支持的币种列表
 * @returns {Promise}
 */
export const GetSupportedCurrencies = () => {
  return get('/v1/currencys');
};

/**
 * 设置币种
 * @param {string} currency 币种代码
 * @returns {Promise}
 */
export const SetCurrency = (currency) => {
  return get(`/v1/currency/${currency}`);
};

/**
 * 设置语言
 * @param {string} lang 语言代码
 * @returns {Promise}
 */
export const SetLanguage = (lang) => {
  return get(`/v1/lang/${lang}`);
};

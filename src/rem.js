(function () {
  // 设备类型检测
  function detectDevice() {
    const width = window.innerWidth;
    const ua = navigator.userAgent.toLowerCase();
    const isMobileUA =
      /android|webos|iphone|ipad|ipod|blackberry|iemobile|opera mini/i.test(ua);

    if (width <= 768 || isMobileUA) {
      return 'mobile';
    } else if (width <= 1024) {
      return 'tablet';
    } else {
      return 'desktop';
    }
  }

  // PC端rem设置
  function setDesktopRem() {
    const baseWidth = 1920; // PC设计稿宽度
    const baseFontSize = 10; // 基础字体大小
    const clientWidth = document.documentElement.clientWidth;
    const fontSize = Math.max(
      8,
      Math.min(16, (clientWidth / baseWidth) * baseFontSize)
    );
    document.documentElement.style.fontSize = fontSize + 'px';
    document.documentElement.setAttribute('data-device', 'desktop');
  }

  // 平板端rem设置
  function setTabletRem() {
    const baseWidth = 768; // 平板设计稿宽度
    const baseFontSize = 12; // 基础字体大小
    const clientWidth = document.documentElement.clientWidth;
    const fontSize = Math.max(
      10,
      Math.min(18, (clientWidth / baseWidth) * baseFontSize)
    );
    document.documentElement.style.fontSize = fontSize + 'px';
    document.documentElement.setAttribute('data-device', 'tablet');
  }

  // 移动端rem设置
  function setMobileRem() {
    const baseWidth = 375; // 移动端设计稿宽度
    const baseFontSize = 10; // 基础字体大小，从16降低到10
    const clientWidth = document.documentElement.clientWidth;
    // 限制最小和最大字体大小，避免极端情况
    const fontSize = Math.max(
      6, // 降低最小字体大小
      Math.min(12, (clientWidth / baseWidth) * baseFontSize) // 降低最大字体大小
    );
    document.documentElement.style.fontSize = fontSize + 'px';
    document.documentElement.setAttribute('data-device', 'mobile');
  }

  // 统一的rem设置函数
  function setRem() {
    const deviceType = detectDevice();

    switch (deviceType) {
      case 'mobile':
        setMobileRem();
        break;
      case 'tablet':
        setTabletRem();
        break;
      case 'desktop':
      default:
        setDesktopRem();
        break;
    }

    // 触发自定义事件，通知其他组件设备类型变化
    const event = new CustomEvent('devicechange', {
      detail: {
        deviceType,
        width: window.innerWidth,
        height: window.innerHeight,
      },
    });
    window.dispatchEvent(event);
  }

  // 初始化
  setRem();

  // 监听窗口大小变化
  let resizeTimer;
  window.addEventListener('resize', function (e) {
    // 防抖处理，避免频繁触发
    clearTimeout(resizeTimer);
    resizeTimer = setTimeout(() => {
      setRem();
    }, 100);
  });

  // 监听设备方向变化
  window.addEventListener('orientationchange', function () {
    setTimeout(setRem, 200);
  });

  // 页面可见性变化时重新计算（解决一些兼容性问题）
  document.addEventListener('visibilitychange', function () {
    if (!document.hidden) {
      setTimeout(setRem, 100);
    }
  });

  // 导出到全局，方便调试
  window.setRem = setRem;
  window.detectDevice = detectDevice;
})();

import { defineStore } from 'pinia';
import { ref, computed } from 'vue';

export const useDeviceStore = defineStore('device', () => {
  // 基础状态
  const isMobile = ref(false);
  const isTablet = ref(false);
  const isDesktop = ref(true);
  const deviceType = ref('desktop'); // 'mobile', 'tablet', 'desktop'
  const screenWidth = ref(window.innerWidth);
  const screenHeight = ref(window.innerHeight);

  // 方向状态
  const orientation = ref('landscape'); // 'portrait', 'landscape'

  // 计算属性
  const isSmallScreen = computed(() => screenWidth.value <= 768);
  const isMediumScreen = computed(
    () => screenWidth.value > 768 && screenWidth.value <= 1024
  );
  const isLargeScreen = computed(() => screenWidth.value > 1024);
  const isPortrait = computed(() => screenHeight.value > screenWidth.value);
  const isLandscape = computed(() => screenWidth.value > screenHeight.value);

  // 断点计算
  const breakpoint = computed(() => {
    const width = screenWidth.value;
    if (width < 576) return 'xs';
    if (width < 768) return 'sm';
    if (width < 992) return 'md';
    if (width < 1200) return 'lg';
    if (width < 1400) return 'xl';
    return 'xxl';
  });

  // 通过User Agent进行设备检测
  const detectUserAgent = () => {
    const ua = navigator.userAgent.toLowerCase();
    const isMobileUA =
      /android|webos|iphone|ipad|ipod|blackberry|iemobile|opera mini/i.test(ua);
    const isTabletUA = /ipad|android(?=.*tablet)|tablet/i.test(ua);

    return {
      isMobile: isMobileUA && !isTabletUA,
      isTablet: isTabletUA,
      isDesktop: !isMobileUA && !isTabletUA,
      userAgent: ua,
    };
  };

  // 综合设备检测
  const updateDeviceInfo = () => {
    const width = window.innerWidth;
    const height = window.innerHeight;
    const uaInfo = detectUserAgent();

    // 更新屏幕尺寸
    screenWidth.value = width;
    screenHeight.value = height;

    // 更新设备方向
    orientation.value = height > width ? 'portrait' : 'landscape';

    // 综合判断设备类型
    if (uaInfo.isMobile || width <= 768) {
      isMobile.value = true;
      isTablet.value = false;
      isDesktop.value = false;
      deviceType.value = 'mobile';
    } else if (uaInfo.isTablet || (width > 768 && width <= 1024)) {
      isMobile.value = false;
      isTablet.value = true;
      isDesktop.value = false;
      deviceType.value = 'tablet';
    } else {
      isMobile.value = false;
      isTablet.value = false;
      isDesktop.value = true;
      deviceType.value = 'desktop';
    }
  };

  // 初始化设备检测
  const initDevice = () => {
    updateDeviceInfo();

    // 监听窗口大小变化
    window.addEventListener('resize', updateDeviceInfo);

    // 监听设备方向变化
    window.addEventListener('orientationchange', () => {
      setTimeout(updateDeviceInfo, 100); // 延迟一点确保获取到正确的尺寸
    });
  };

  // 清理事件监听
  const cleanup = () => {
    window.removeEventListener('resize', updateDeviceInfo);
    window.removeEventListener('orientationchange', updateDeviceInfo);
  };

  // 设置设备类型（手动设置）
  const setDeviceType = (type) => {
    deviceType.value = type;
    isMobile.value = type === 'mobile';
    isTablet.value = type === 'tablet';
    isDesktop.value = type === 'desktop';
  };

  // 获取设备信息摘要
  const getDeviceInfo = () => {
    return {
      deviceType: deviceType.value,
      isMobile: isMobile.value,
      isTablet: isTablet.value,
      isDesktop: isDesktop.value,
      screenWidth: screenWidth.value,
      screenHeight: screenHeight.value,
      orientation: orientation.value,
      breakpoint: breakpoint.value,
      isSmallScreen: isSmallScreen.value,
      isMediumScreen: isMediumScreen.value,
      isLargeScreen: isLargeScreen.value,
    };
  };

  return {
    // 状态
    isMobile,
    isTablet,
    isDesktop,
    deviceType,
    screenWidth,
    screenHeight,
    orientation,

    // 计算属性
    isSmallScreen,
    isMediumScreen,
    isLargeScreen,
    isPortrait,
    isLandscape,
    breakpoint,

    // 方法
    initDevice,
    updateDeviceInfo,
    setDeviceType,
    getDeviceInfo,
    cleanup,
    detectUserAgent,
  };
});

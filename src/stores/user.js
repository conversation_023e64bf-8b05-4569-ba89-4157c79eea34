import { defineStore } from 'pinia';
import { ref, computed } from 'vue';

export const useUserStore = defineStore(
  'user',
  () => {
    // 状态
    const token = ref(localStorage.getItem('token') || '');
    const userInfo = ref(
      JSON.parse(localStorage.getItem('userInfo') || 'null')
    );
    const isLoggedIn = computed(() => !!token.value);

    // 设置token
    const setToken = (newToken) => {
      token.value = newToken;
      if (newToken) {
        localStorage.setItem('token', newToken);
      } else {
        localStorage.removeItem('token');
      }
    };

    // 设置用户信息
    const setUserInfo = (info) => {
      userInfo.value = info;
      if (info) {
        localStorage.setItem('userInfo', JSON.stringify(info));
      } else {
        localStorage.removeItem('userInfo');
      }
    };

    // 登录
    const login = (loginData) => {
      const { token: newToken, user, ...otherData } = loginData;
      setToken(newToken);
      setUserInfo(user || otherData);
    };

    // 登出
    const logout = () => {
      setToken('');
      setUserInfo(null);
      // 清除其他相关的localStorage数据
      localStorage.removeItem('token');
      localStorage.removeItem('userInfo');
    };

    // 更新用户信息
    const updateUserInfo = (newInfo) => {
      const updatedInfo = { ...userInfo.value, ...newInfo };
      setUserInfo(updatedInfo);
    };

    return {
      // 状态
      token,
      userInfo,
      isLoggedIn,
      // 方法
      setToken,
      setUserInfo,
      login,
      logout,
      updateUserInfo,
    };
  },
  {
    // 持久化选项（可选，如果需要更复杂的持久化策略）
    persist: false, // 我们手动管理localStorage
  }
);

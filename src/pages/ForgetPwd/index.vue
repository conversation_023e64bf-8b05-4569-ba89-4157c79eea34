<template>
  <div class="forget-pwd-view" :class="{ 'mobile-view': deviceStore.isMobile }">
    <div class="back-btn" @click="goBack">
      <img src="@assets/back.png" alt="返回" />
      {{ t('login.forget') }}
    </div>
    <div
      class="forget-pwd-view_main"
      :class="{ 'mobile-main': deviceStore.isMobile }"
    >
      <div
        class="forget-pwd-view_main_form"
        :class="{ 'mobile-form': deviceStore.isMobile }"
      >
        <div class="forget-pwd-view_main_form_title">
          {{ t('qing_gen_ju_ti_shi_zhao_hui_nin_de_mi_ma') }}
        </div>
        <div class="forget-pwd-view_main_form_tip">
          {{ t('qing_shu_ru_you_xiang_di_zhi_huo_qu_yan_zheng_ma') }}
        </div>
        <div class="forget-pwd-view_main_form_box">
          <div class="forget-pwd-view_main_form_box_input">
            <input
              class="forget-pwd-view_main_form_box_input_val"
              :placeholder="t('login.placeholder')"
              v-model="form.email"
              type="text"
            />
          </div>
          <div class="forget-pwd-view_main_form_code">
            <div class="forget-pwd-view_main_form_code_input">
              <input
                class="forget-pwd-view_main_form_code_input_val"
                :placeholder="t('shu_ru_yan_zheng_ma')"
                v-model="form.code"
                type="text"
              />
              <div
                class="forget-pwd-view_main_form_code_button"
                @click="sendVerificationCode"
                :disabled="cooldown > 0"
                :style="{
                  color: cooldown > 0 ? '#70707B' : '#6E4AEB',
                }"
              >
                {{
                  cooldown > 0
                    ? `${cooldown}${t('miao_hou_zhong_shi')}`
                    : t('fa_song_yan_zheng_ma')
                }}
              </div>
            </div>
          </div>
          <div class="forget-pwd-view_main_form_box_input">
            <input
              class="forget-pwd-view_main_form_box_input_val"
              :placeholder="t('xin_mi_ma')"
              v-model="form.newPassword"
              type="password"
            />
          </div>
          <div class="forget-pwd-view_main_form_box_input">
            <input
              class="forget-pwd-view_main_form_box_input_val"
              :placeholder="t('zai_ci_shu_ru_xin_mi_ma')"
              v-model="form.confirmPassword"
              type="password"
            />
          </div>
        </div>
        <div
          class="forget-pwd-view_main_form_btn"
          :class="{ 'mobile-btn': deviceStore.isMobile }"
          @click="resetPassword"
        >
          {{ t('zhong_zhi_mi_ma') }}
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { reactive, ref, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import { useI18n } from 'vue-i18n';
import { ElMessage } from 'element-plus';
import { useDeviceStore } from '@/stores/device';
import { SendCode, ResetPassword } from '@/api/auth';

const deviceStore = useDeviceStore();
const { t } = useI18n();
const router = useRouter();

// 确保在组件挂载时初始化设备检测
onMounted(() => {
  deviceStore.initDevice();
});

// 表单数据
const form = reactive({
  email: '',
  code: '',
  newPassword: '',
  confirmPassword: '',
});

// 验证码倒计时
const cooldown = ref(0);
const startCooldown = () => {
  cooldown.value = 60;
  const timer = setInterval(() => {
    cooldown.value--;
    if (cooldown.value <= 0) {
      clearInterval(timer);
    }
  }, 1000);
};

// 发送验证码
const sendVerificationCode = () => {
  if (cooldown.value > 0) {
    return;
  }
  if (!form.email) {
    ElMessage({
      message: t('qing_shu_ru_you_xiang_di_zhi'),
      type: 'warning',
    });
    return;
  }

  const emailReg =
    /^[a-zA-Z0-9_.-]+@[a-zA-Z0-9-]+(\.[a-zA-Z0-9-]+)*\.[a-zA-Z0-9]{2,6}$/;
  if (!emailReg.test(form.email)) {
    ElMessage({
      message: t('qing_shu_ru_zheng_que_de_you_xiang'),
      type: 'warning',
    });
    return;
  }

  // 调用发送验证码的API
  SendCode({ email: form.email })
    .then((res) => {
      console.log('验证码发送响应:', res);
      startCooldown();
      ElMessage({
        message: t('yan_zheng_ma_yi_fa_song'),
        type: 'success',
      });
    })
    .catch((error) => {
      console.error('发送验证码失败:', error);
    });
};

// 重置密码
const resetPassword = () => {
  // 表单验证
  if (!form.email || !form.code || !form.newPassword || !form.confirmPassword) {
    ElMessage({
      message: t('qing_tian_xie_suo_you_bi_tian_xiang'),
      type: 'warning',
    });
    return;
  }

  const emailReg =
    /^[a-zA-Z0-9_.-]+@[a-zA-Z0-9-]+(\.[a-zA-Z0-9-]+)*\.[a-zA-Z0-9]{2,6}$/;
  if (!emailReg.test(form.email)) {
    ElMessage({
      message: t('qing_shu_ru_zheng_que_de_you_xiang'),
      type: 'warning',
    });
    return;
  }

  if (form.newPassword !== form.confirmPassword) {
    ElMessage({
      message: t('liang_ci_shu_ru_de_mi_ma_bu_yi_zhi'),
      type: 'warning',
    });
    return;
  }

  // 调用重置密码的API
  ResetPassword({
    email: form.email,
    code: form.code,
    password: form.newPassword,
    password_confirmation: form.confirmPassword,
  })
    .then((res) => {
      console.log('密码重置响应:', res);
      ElMessage({
        message: t(
          'mi_ma_zhong_zhi_cheng_gong_qing_shi_yong_xin_mi_ma_deng_lu'
        ),
        type: 'success',
      });
      router.push('/login');
    })
    .catch((error) => {
      console.error('密码重置失败:', error);
    });
};

// 返回上一页
const goBack = () => {
  router.back();
};
</script>

<style lang="scss" scoped>
@import url('./index.scss');
</style>

.forget-pwd-view {
  padding: 2.9rem 30.1rem 13.6rem 30.1rem;
  background: #f6f6f6;
  box-sizing: border-box;
  flex: 1;
  &_title {
    height: 1.9rem;
    font-family: PingFang SC, PingFang SC;
    font-weight: 400;
    font-size: 1.4rem;
    color: #70707b;
    margin-bottom: 2.9rem;
  }
  &_main {
    height: 71.8rem;
    background: #ffffff;
    border-radius: 1.9rem;
    padding: 19.1rem 23.3rem;
    box-sizing: border-box;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    &_form {
      width: 34.9rem;
      &_title {
        font-family: PingFang SC, PingFang SC;
        font-weight: 600;
        font-size: 2.6rem;
        color: #242426;
        text-align: center;
        line-height: 3.3rem;
        margin-bottom: 0.5rem;
      }
      &_tip {
        font-family: PingFang SC, PingFang SC;
        font-weight: 400;
        font-size: 1.8rem;
        color: #70707b;
        text-align: center;
        margin-bottom: 5.8rem;
      }
      &_box {
        margin-bottom: 2.4rem;
        &_input {
          margin-bottom: 2.9rem;
          width: 100%;
          height: 5rem;
          background: #f3f4f8;
          border-radius: 3.1rem;
          position: relative;
          box-sizing: border-box;
          &_val {
            width: 100%;
            height: 5rem;
            background: #f3f4f8;
            border-radius: 3.1rem;
            padding-left: 2.3rem;
            box-sizing: border-box;
            font-family: PingFang SC, PingFang SC;
            font-weight: 400;
            font-size: 1.6rem;
            color: #323232;
            border: 0;
            // 一行显示  最大宽度
            // white-space: nowrap;
            // overflow: hidden;
            // text-overflow: ellipsis;
          }
          &_val:focus {
            outline: 0;
          }
          &_val::placeholder {
            color: #70707b;
          }
        }
        &_input:last-child {
          margin-bottom: 0;
        }
      }
      &_btn {
        cursor: pointer;
        width: 34.9rem;
        height: 5rem;
        background: #6e4aeb;
        border-radius: 3.1rem;
        display: flex;
        align-items: center;
        justify-content: center;
        font-family: PingFang SC, PingFang SC;
        font-weight: 600;
        font-size: 1.7rem;
        color: #ffffff;
        margin-top: 3rem;
      }
      &_code {
        display: flex;
        justify-content: space-between;
        margin-bottom: 2.9rem;

        &_input {
          width: 100%;
          height: 5rem;
          background: #f3f4f8;
          border-radius: 3.1rem;
          position: relative;
          box-sizing: border-box;
          display: flex;
          align-items: center;
          &_val {
            width: 100%;
            height: 5rem;
            background: #f3f4f8;
            border-radius: 3.1rem;
            padding-left: 2.3rem;
            box-sizing: border-box;
            font-family: PingFang SC, PingFang SC;
            font-weight: 400;
            font-size: 1.6rem;
            color: #323232;
            border: 0;
          }
          &_val:focus {
            outline: 0;
          }
          &_val::placeholder {
            color: #70707b;
          }
        }
        &_button {
          cursor: pointer;
          font-family: PingFang SC, PingFang SC;
          font-weight: 600;
          font-size: 1.4rem;
          color: #6e4aeb;
          flex-shrink: 0;
          margin-right: 2rem;
        }
      }
    }
  }
}

// 移动端样式
.mobile-view {
  padding: 1.5rem;
  min-height: 100vh;
  .forget-pwd-view_title {
    margin-bottom: 1.5rem;
  }
  .forget-pwd-view_main_form_title {
    font-family: PingFang SC, PingFang SC;
    font-weight: 600;
    font-size: 2rem;
    color: #242426;
    text-align: center;
    line-height: 3.3rem;
  }

  .forget-pwd-view_main_form_tip {
    font-size: 1.4rem;
  }
}

.mobile-main {
  height: auto;
  min-height: 45rem;
  padding: 2rem 1.5rem;
  background-color: #fff;
  border-radius: 1rem;
}

.mobile-form {
  width: 100%;
  padding: 0;

  .forget-pwd-view_main_form_box {
    margin-bottom: 2rem;
  }
}

.mobile-btn {
  width: 100%;
  margin-top: 3.5rem;
}

.back-btn {
  display: flex;
  align-items: center;
  cursor: pointer;
  font-family: PingFang SC, PingFang SC;
  font-weight: 400;
  font-size: 1.4rem;
  color: #70707b;
  margin-bottom: 2.9rem;
  img {
    width: 1.2rem;
    height: 1.2rem;
    margin-right: 0.5rem;
  }
}

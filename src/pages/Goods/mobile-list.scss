/* 移动端适配样式 */
@media screen and (max-width: 768px) {
  .goods-list-main {
    .banner-box {
      height: auto !important;
      min-height: 35rem;
      padding: 3rem 1.5rem !important;
      gap: 3rem !important;

      &_text {
        &_title {
          font-size: 2.6rem !important;
          line-height: 1.3 !important;
          margin-bottom: 1rem;
        }

        &_desc {
          font-size: 1.6rem !important;
          line-height: 1.5 !important;
        }
      }

      .download_box_img {
        width: 100% !important;
        height: auto !important;
        max-height: 30rem;
        object-fit: contain !important;
      }
    }

    .goods_view {
      padding: 3rem 1.5rem !important;

      &_title {
        font-size: 2.4rem !important;
        height: auto !important;
        line-height: 1.3 !important;
        margin-bottom: 2rem !important;
      }

      /* 移动端两列布局 */
      .product_list {
        display: grid !important;
        grid-template-columns: 1fr 1fr !important; /* 两列布局 */
        gap: 1rem !important;
        width: 100% !important;

        &_item {
          width: 100% !important;
          height: auto !important;
          padding: 1rem !important;
          margin: 0 !important;
          border-radius: 1rem !important;

          &_img {
            width: 100% !important;
            height: auto !important;
            margin-bottom: 0.8rem !important;
            max-height: 12rem !important;
            border-radius: 0.8rem !important;
          }

          &_name {
            font-size: 1.4rem !important;
            height: auto !important;
            line-height: 1.3 !important;
            margin-bottom: 0.8rem !important;
            max-height: 3.6rem !important;
            overflow: hidden !important;
            display: -webkit-box !important;
            -webkit-line-clamp: 2 !important;
            -webkit-box-orient: vertical !important;
            line-clamp: 2 !important; /* 标准属性，配合-webkit-前缀实现兼容性 */
          }
        }

        &_info {
          flex-direction: column !important;
          align-items: flex-start !important;
          gap: 0.5rem !important;

          &_price {
            font-size: 1.4rem !important;
            height: 2.4rem !important;
            padding: 0 0.6rem !important;
          }

          &_sale {
            font-size: 1.2rem !important;
          }
        }
      }
    }
  }
}

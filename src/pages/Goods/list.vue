<template>
  <div class="goods-list-main">
    <div
      class="banner-box"
      :style="
        deviceStore.isMobile
          ? 'padding:1.6rem;flex-wrap:wrap;height:auto;gap:3rem;min-height:30rem;'
          : ''
      "
    >
      <div
        class="banner-box_text"
        :style="deviceStore.isMobile ? 'margin-bottom:1rem;' : ''"
      >
        <div
          class="banner-box_text_title"
          :style="
            deviceStore.isMobile
              ? 'font-size:2.6rem;line-height:1.3;margin-bottom:1rem;'
              : ''
          "
        >
          {{ t('an_quan_guan_li_nin_de_shu_zi_zi_chan') }}
        </div>
        <div
          class="banner-box_text_desc"
          :style="
            deviceStore.isMobile ? 'font-size:1.6rem;line-height:1.5;' : ''
          "
        >
          {{ t('cong_yi_ge_di_fang_guan_li_he_zhi_ya_nin_de_shu_zi_zi_chan') }}
        </div>
      </div>
      <img
        src="@assets/img_banner-s.png"
        class="download_box_img"
        :style="
          deviceStore.isMobile
            ? 'width:100%;height:auto;max-height:20rem;object-fit:contain;'
            : ''
        "
      />
    </div>
    <div
      class="goods_view"
      :style="deviceStore.isMobile ? 'padding:2rem 1rem;' : ''"
    >
      <div
        class="goods_view_title"
        :style="
          deviceStore.isMobile
            ? 'margin-bottom:2rem;font-size:2.4rem;height:auto;line-height:1.3;'
            : ''
        "
      >
        {{ t('goods.title') }}
      </div>
      <goodsList :products="productList" :singleColumn="false" />
    </div>
  </div>
</template>
<script setup>
import { ref, onMounted, watch } from 'vue';
import { GetProductList } from '@api/product';
import goodsList from '@components/goodsList.vue';
import { useI18n } from 'vue-i18n';
import { currencyUpdated } from '@utils/eventBus';
import { useDeviceStore } from '@/stores/device';
import { useRoute } from 'vue-router';
const deviceStore = useDeviceStore();

const route = useRoute();

const { t } = useI18n();
const productList = ref([]);

// 获取商品列表数据
const loadProductList = async () => {
  try {
    console.log('加载商品列表...', route.query);
    const params = {};
    if (route.query.keyword) {
      params.name = route.query.keyword;
    }
    const res = await GetProductList(params);
    console.log('商品列表数据', res);
    if (res && res.items) {
      productList.value = res.items;
    }
  } catch (error) {
    console.error('获取商品列表失败:', error);
  }
};

onMounted(() => {
  loadProductList();
});

// 监听币种变化，重新加载商品列表
watch(currencyUpdated, () => {
  loadProductList();
});
</script>
<style lang="scss" scoped>
@import url('./list.scss');
@import url('./mobile-list.scss');
</style>

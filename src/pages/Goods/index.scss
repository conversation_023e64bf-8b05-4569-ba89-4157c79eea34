.goods_box {
  background: #f6f6f6;
  padding-top: 3rem;
  flex: 1;
  &_title {
    margin-bottom: 3.6rem;
    font-family: PingFang SC, PingFang SC;
    font-weight: 400;
    font-size: 1.4rem;
    line-height: 2rem;
    color: #70707b;
    padding: 0 30.1rem;
  }
  &_info {
    padding: 0 30.1rem 8rem 30.1rem;
    box-sizing: border-box;
    display: flex;
    & &_img {
      display: flex;
      width: 60.6rem;
      gap: 0; /* 移除多余间距，因为我们已经在子元素中添加了足够的间距 */
      &_list {
        height: 48.9rem;
        overflow-y: auto;
        overflow-x: hidden;
        display: flex;
        flex-direction: column;
        width: 9.6rem; /* 增加宽度，为小图标和边框预留足够空间 */
        padding: 0.4rem; /* 添加内边距，防止边框被裁剪 */
        margin-right: 2.4rem; /* 稍微减少右侧间距，保持整体布局 */
        scrollbar-width: none; /* Firefox */
        -ms-overflow-style: none; /* IE and Edge */

        /* 完全隐藏WebKit浏览器的滚动条 */
        &::-webkit-scrollbar {
          display: none; /* Chrome, Safari, Opera */
        }

        &_single {
          width: 8rem; /* 减小宽度，为边框预留空间 */
          height: 8rem; /* 减小高度，为边框预留空间 */
          margin: 0 0 1.2rem auto; /* 居中显示并保持底部间距 */
          padding: 0.2rem; /* 添加内边距，确保内容与边框有距离 */
          cursor: pointer;
          display: flex;
          align-items: center;
          justify-content: center;
          border: 1px solid transparent;
          box-sizing: border-box;
          border-radius: 1.2rem; /* 添加圆角 */
          transition: all 0.2s;
          &_img {
            max-width: 100%;
            max-height: 100%;
            object-fit: contain;
            border-radius: 1.8rem; /* 图片也添加圆角，稍微小于容器的圆角 */
          }
        }

        &_single:hover {
          border-color: #333333;
        }

        &_single.active {
          border: 1px solid #70707b;
          outline: none;
        }
      }

      &_big {
        width: 48.9rem;
        &_img {
          width: 48.9rem;
          height: 48.9rem;
        }
      }
    }
    &_base {
      height: 48.9rem;
      padding-left: 12rem;
      flex: 1;
      box-sizing: border-box;
      display: flex;
      flex-direction: column;
      justify-content: center;
      &_name {
        font-family: PingFang SC, PingFang SC;
        font-weight: 600;
        font-size: 6.4rem;
        color: #000000;
        margin-bottom: 1.1rem;
      }
      &_price {
        height: 5rem;
        line-height: 5rem;
        font-family: PingFang SC, PingFang SC;
        font-weight: 600;
        font-size: 3.6rem;
        color: #000000;
        margin-bottom: 3.2rem;
      }
      &_property {
        height: 2.8rem;
        display: flex;
        align-items: center;
        font-family: PingFang SC, PingFang SC;
        font-weight: 400;
        font-size: 1.6rem;
        color: #70707b;
      }
      &_action {
        display: flex;
        align-items: center;
        position: relative;
        margin-bottom: 1.6rem;
        margin-top: 3.2rem;
        &_item {
          padding: 0 2.4rem;
          display: flex;
          align-items: center;
          justify-content: center;
          cursor: pointer;
          font-family: PingFang SC;
          height: 5.9rem;
          outline: 0;
          text-align: center;
          font-family: PingFang SC, PingFang SC;
          font-weight: 600;
          font-size: 1.8rem;
          cursor: pointer;
          border-radius: 3.2rem;
          box-sizing: border-box;
          &_icon {
            margin-left: 1.6rem;
            width: 2rem;
            height: 2rem;
          }
        }
        &_item.addcart {
          background: #6e4aeb;
          margin-right: 2rem;
          color: #ffffff;
        }
        &_item.buynow {
          color: #242426;
          border: 0.1rem solid #242426;
        }
      }
      &_collect {
        height: 2.2rem;
        display: flex;
        align-items: center;
        cursor: pointer;
        font-family: PingFang SC, PingFang SC;
        font-weight: 600;
        font-size: 1.8rem;
        color: #242426;
        &_icon {
          width: 2rem;
          height: 2rem;
          margin-right: 1rem;
        }
      }
    }
  }
}

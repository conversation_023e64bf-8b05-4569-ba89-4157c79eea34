/* 移动端适配样式 */
@media (max-width: 767px) {
  .cart_view {
    padding: 1.5rem 1.2rem 6rem 1.2rem;

    &_title {
      font-size: 1.6rem;
      text-align: center;
      margin-bottom: 1.5rem;
    }

    &_step {
      height: auto;
      padding: 1.5rem 1rem;
      margin-bottom: 1rem;
      flex-wrap: wrap;

      &_item {
        font-size: 1.4rem;
        margin-bottom: 0.5rem;

        &_dot {
          width: 2.4rem;
          height: 2.4rem;
          font-size: 1.2rem;
        }

        &_img {
          width: 2.4rem;
          height: 2.4rem;
        }
      }

      &_line {
        width: 3rem;
        margin: 0 0.3rem;
      }
    }

    &_box {
      flex-direction: column;

      &_cartList,
      &_container {
        width: 100%;
        padding: 1.5rem 1rem;
        margin-bottom: 1rem;
      }

      &_title {
        font-size: 1.6rem;
        margin-bottom: 1.5rem;
      }

      &_list {
        width: 100%;
        margin-left: 0;
        margin-top: 1rem;
      }

      &_btn {
        width: 100%;
      }

      &_mobile_select {
        display: flex;
        padding: 1rem 0;
        margin-bottom: 1rem;
      }
    }

    &_pay {
      flex-direction: column;
      align-items: center;
      padding: 2rem 1.5rem;

      &_img {
        width: 8rem;
        height: 8rem;
        margin-bottom: 1.5rem;
      }

      &_main {
        width: 100%;

        &_title {
          text-align: center;
          font-size: 1.8rem;
          margin-bottom: 1.5rem;
        }

        &_info {
          height: auto;
          padding: 1.5rem;

          &_item {
            font-size: 1.4rem;
            display: flex;
            justify-content: space-between;
            align-items: center;

            span.red {
              font-size: 1.6rem;
            }

            img {
              max-width: 8rem;
              height: auto;
            }
          }
        }

        &_action {
          flex-direction: column;
          width: 100%;

          &_item {
            width: 100%;
            margin-right: 0;
            margin-bottom: 1rem;
          }
        }
      }
    }
  }

  /* 购物车列表在移动端的样式调整 */
  .cart_view_box_tr {
    flex-direction: row;
    align-items: flex-start;

    &_td {
      width: 100% !important;
      margin-bottom: 1rem;

      &.w117 {
        padding-left: 0;
      }

      &.w377 {
        flex-direction: row;

        img {
          width: 6rem;
          height: 6rem;
        }

        .product-info {
          flex: 1;

          &-name {
            font-size: 1.4rem;
            margin-bottom: 0.5rem;
          }

          &-price {
            font-size: 1.6rem;
            color: #c33232;
            margin-bottom: 0.5rem;
          }
        }
      }

      &_action {
        display: flex;
        justify-content: flex-end;
        width: 100%;
      }
    }
  }

  /* 隐藏桌面版表头 */
  .cart_view_box_thead {
    display: none;
  }

  /* 支付方式在移动端的样式 */
  .cart_view_box_payment {
    flex-wrap: wrap;
    gap: 1rem;

    &_item {
      width: calc(50% - 0.5rem);
      height: 5rem;
    }
  }

  /* 配送信息在移动端的样式 */
  .cart_view_box_deliver {
    width: 100%;
    height: auto;
    padding: 1.2rem;
    flex-direction: column;
    text-align: center;

    &_icon {
      margin-bottom: 1rem;
    }

    &_main {
      padding-left: 0;

      &_tip1 {
        font-size: 1.6rem;
      }

      &_tip2 {
        font-size: 1.4rem;
      }
    }
  }

  /* 地址列表在移动端的样式 */
  .cart_view_box_address {
    padding: 1.5rem;

    &_action {
      position: static;
      transform: none;
      justify-content: flex-end;
      margin-top: 1rem;
    }
  }
}

/* 支付图标样式 */
.payment-icon {
  max-height: 3rem;
  object-fit: contain;
}

<template>
  <div class="cart_view">
    <p class="cart_view_title">{{ t('cart.title') }}</p>
    <div class="cart_view_step">
      <div :class="['cart_view_step_item', step <= 3 ? 'active' : '']">
        <img
          v-if="step > 1"
          src="@assets/checked.png"
          class="cart_view_step_item_img"
        />
        <div v-if="step == 1" class="cart_view_step_item_dot">1</div>
        {{ t('cart.cart') }}
      </div>
      <div :class="['cart_view_step_line', step <= 2 ? 'active' : '']"></div>
      <div :class="['cart_view_step_item', step == 2 ? 'active' : '']">
        <img
          v-if="step > 2"
          src="@assets/checked.png"
          class="cart_view_step_item_img"
        />
        <div
          v-else
          :class="['cart_view_step_item_dot', step !== 2 ? 'disable' : '']"
        >
          2
        </div>
        {{ t('cart.confirm') }}
      </div>
      <div :class="['cart_view_step_line', step == 3 ? 'active' : '']"></div>
      <div :class="['cart_view_step_item', step == 3 ? 'active' : '']">
        <div :class="['cart_view_step_item_dot', step !== 3 ? 'disable' : '']">
          3
        </div>
        {{ t('cart.pay') }}
      </div>
    </div>
    <div
      class="cart_view_pay"
      v-if="step == 3"
      :class="{ 'mobile-view': isMobile }"
    >
      <img src="@assets/payIcon.png" class="cart_view_pay_img" />
      <div class="cart_view_pay_main">
        <p class="cart_view_pay_main_title">{{ t('cart.tip') }}</p>
        <div class="cart_view_pay_main_info">
          <div class="cart_view_pay_main_info_item mb8">
            {{ t('cart.orderNo') }}：
            <span>{{ paymentInfo.orderNumber }}</span>
          </div>
          <div class="cart_view_pay_main_info_item mb15">
            {{ t('cart.payAmount') }}：
            <span class="red">{{ paymentInfo.total_format }}</span>
          </div>
          <div class="cart_view_pay_main_info_item">
            {{ t('cart.payType') }}：
            <img
              :src="selectedPaymentIcon"
              :alt="selectedPaymentName"
              class="payment-icon"
            />
          </div>
        </div>
        <div class="cart_view_pay_main_action">
          <div class="cart_view_pay_main_action_item" @click="handlePayment">
            {{ t('cart.toPay') }}
            <img src="@assets/check.png" />
          </div>
          <!-- <div class="cart_view_pay_main_action_item">
                        {{ t('cart.credit') }}
                        <img src="@assets/credit.png" />
                    </div> -->
        </div>
      </div>
    </div>
    <div class="cart_view_box" v-else>
      <div class="cart_view_box_cartList" v-if="step == 1" v-loading="loading">
        <div class="cart_view_box_title">{{ t('cart.subtitle') }}</div>
        <div class="cart_view_box_thead">
          <div class="cart_view_box_thead_th w117">
            <el-checkbox
              v-model="chooseAll"
              @change="handleSelectAll"
              class="check"
            >
              {{ t('cart.chooseAll') }}
            </el-checkbox>
          </div>
          <div class="cart_view_box_thead_th w377">
            {{ t('cart.subtitle') }}
          </div>
          <div class="cart_view_box_thead_th w114">{{ t('cart.num') }}</div>
          <div class="cart_view_box_thead_th w147">{{ t('cart.fee') }}</div>
          <div class="cart_view_box_thead_th w1">{{ t('cart.operate') }}</div>
        </div>
        <div v-if="!loading && cartList.length === 0" class="empty-state">
          {{ t('gou_wu_che_wei_kong') }}
        </div>
        <div v-else>
          <div
            class="cart_view_box_tr"
            v-for="item in cartList"
            :key="item.cart_id"
          >
            <div class="cart_view_box_tr_td w117">
              <el-checkbox
                :model-value="item.selected"
                @change="(checked) => handleSelectItem(item.cart_id, checked)"
              ></el-checkbox>
            </div>
            <div class="cart_view_box_tr_td w377">
              <img :src="item.image_url" />
              <div class="product-info">
                <div class="product-info-name">{{ item.name_format }}</div>
                <div class="product-info-price">{{ item.price_format }}</div>
                <div class="product-info-quantity">
                  <el-input-number
                    :model-value="item.quantity"
                    :min="1"
                    :max="item.stock"
                    @change="
                      (value) => handleUpdateQuantity(item.cart_id, value)
                    "
                    size="small"
                  />
                </div>
              </div>
            </div>
            <div class="cart_view_box_tr_td w114" style="display: none">
              <el-input-number
                :model-value="item.quantity"
                :min="1"
                :max="item.stock"
                @change="(value) => handleUpdateQuantity(item.cart_id, value)"
                size="small"
              />
            </div>
            <div class="cart_view_box_tr_td w147" style="display: none">
              {{ item.price_format }}
            </div>
            <div class="cart_view_box_tr_td w1">
              <div class="cart_view_box_tr_td_action">
                <img
                  src="@assets/trash.png"
                  class="cart_view_item_del"
                  @click="handleRemoveItem(item.cart_id)"
                />
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="cart_view_box_container" v-else>
        <div class="cart_view_box_title">{{ t('cart.address') }}</div>

        <!-- 地址列表 -->
        <div v-if="addressList.length > 0">
          <div
            v-for="address in addressList"
            :key="address.id"
            class="cart_view_box_address"
            :class="{ selected: selectedAddressId === address.id }"
            @click="handleSelectAddress(address.id)"
          >
            <div class="cart_view_box_address_user">
              {{ address.name }}&nbsp;&nbsp;{{ address.phone }}
              <span v-if="address.is_default" class="default-tag">{{
                t('cart.default')
              }}</span>
            </div>
            <div class="cart_view_box_address_detail">
              {{ address.full_address }}
            </div>
            <div class="cart_view_box_address_action">
              <img
                src="@assets/edit.png"
                class="cart_view_box_address_action_icon"
                @click.stop="handleEditAddress(address)"
              />
              <img
                src="@assets/trash.png"
                class="cart_view_box_address_action_icon"
                @click.stop="handleDeleteAddress(address.id)"
              />
            </div>
          </div>
        </div>

        <div class="cart_view_box_add" @click="handleAddAddress">
          <img src="@assets/add.png" class="cart_view_box_add_img" />
          {{ t('cart.add') }}
        </div>
        <div class="cart_view_box_title">{{ t('cart.paymentType') }}</div>
        <div class="cart_view_box_payment">
          <div
            v-for="payment in paymentMethods"
            :key="payment.id"
            class="cart_view_box_payment_item"
            :class="{ selected: selectedPaymentMethod === payment.id }"
            @click="handleSelectPayment(payment.id)"
          >
            <img
              :src="payment.icon"
              :alt="payment.name"
              class="cart_view_box_payment_item_icon"
            />
          </div>
        </div>
        <div class="cart_view_box_title">{{ t('cart.deliver') }}</div>
        <div class="cart_view_box_deliver">
          <img src="@assets/icon.png" class="cart_view_box_deliver_icon" />
          <div class="cart_view_box_deliver_main">
            <div class="cart_view_box_deliver_main_tip1">
              {{ t('cart.tip1') }}
            </div>
            <div class="cart_view_box_deliver_main_tip2">
              {{ t('cart.tip2') }}
            </div>
          </div>
        </div>
        <div class="cart_view_box_title">{{ t('cart.note') }}</div>
        <textarea
          class="cart_view_box_textarea"
          :placeholder="t('cart.noteplaceholder')"
        />
      </div>
      <div class="cart_view_box_list">
        <div class="cart_view_box_titles">
          {{ step == 1 ? t('cart.total') : t('cart.tips') }}
          <span class="cart_view_box_titles_num">{{ selectedCount }}</span>
        </div>
        <div class="cart_view_box_line"></div>
        <div v-if="step == 2">
          <div
            class="cart_view_box_list_item"
            v-for="item in selectedItems"
            :key="item.cart_id"
          >
            <img :src="item.image_url" class="cart_view_box_list_item_img" />
            <div class="cart_view_box_list_item_main">
              <p class="cart_view_box_list_item_main_name">
                {{ item.name_format }}
              </p>
              <p class="cart_view_box_list_item_main_info">
                {{ item.price_format }}
                <span>x {{ item.quantity }}</span>
              </p>
            </div>
          </div>
        </div>
        <div class="cart_view_box_info">
          <span>{{ t('cart.all') }}</span>
          {{ totalCount }}
        </div>
        <div class="cart_view_box_info">
          <span>{{ t('cart.choose') }}</span>
          {{ selectedCount }}
        </div>
        <div class="cart_view_box_lines"></div>
        <div class="cart_view_box_all">
          {{ t('cart.total') }}
          <span>{{ totalAmount }}</span>
        </div>
        <!-- 移动端全选 - 只在第一步且是移动设备时显示 -->
        <div class="cart_view_box_mobile_select" v-if="step == 1">
          <el-checkbox
            v-model="chooseAll"
            @change="handleSelectAll"
            class="mobile-check"
          >
            {{ t('cart.chooseAll') }} {{ t('yi_xuan') }} {{ selectedCount }}
          </el-checkbox>
        </div>
        <div
          class="cart_view_box_btn"
          @click="handleNext"
          :class="{ disabled: step == 1 && selectedCount === 0 }"
        >
          {{ step == 1 ? t('cart.toPayment') : t('cart.submit') }}
        </div>
      </div>
    </div>

    <!-- 地址弹框 -->
    <AddressDialog
      v-model:visible="addressDialogVisible"
      :address-id="editAddressId"
      :address-data="currentAddress"
      @success="handleAddressSuccess"
    />
  </div>
</template>
<script setup>
import { ref, onMounted, computed, watch } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { useI18n } from 'vue-i18n';
import { ElMessage, ElMessageBox } from 'element-plus';
import {
  GetCartList,
  SelectCartItems,
  UnselectCartItems,
  UpdateCart,
  RemoveCartItem,
  GetMiniCart,
} from '@api/cart';
import { GetCheckoutInfo, ConfirmOrder, UpdateCheckout } from '@api/checkout';
import { GetAddressList, DeleteAddress, GetAddressDetail } from '@api/address';
import { GetOrderPayInfo } from '@api/order';
// import { PayWithPayPal, PayWithStripe, PayWithBitPay, PayWithWePay, GetPaymentStatus } from '@api/payment';
import { PaymentFactory } from '@utils/payment';
import AddressDialog from '@components/AddressDialog.vue';
import { useDevice } from '@/composables/useDevice';

// 导入支付方式图标
import paypalIcon from '@assets/paypal.png';
import stripeIcon from '@assets/<EMAIL>';
import bitpayIcon from '@assets/<EMAIL>';
import wepayIcon from '@assets/<EMAIL>';

const { t } = useI18n();
const route = useRoute();
const router = useRouter();
const { isMobile } = useDevice();

const step = ref(parseInt(route.query.step) || 1);
const cartList = ref([]);
const miniCartData = ref({});
const loading = ref(false);
const chooseAll = ref(false);
const paymentInfo = ref(null);
const checkoutInfo = ref(null); // 添加结算信息
const subTotal = ref(''); // 添加商品总计变量

// 地址相关数据
const addressList = ref([]);
const selectedAddressId = ref(null);
const addressDialogVisible = ref(false);
const editAddressId = ref(null);
const payInfo = ref(null);

// 支付方式相关数据
const selectedPaymentMethod = ref('paypal');
const paymentMethods = ref([
  {
    id: 'paypal',
    name: 'PayPal',
    icon: paypalIcon,
  },
  {
    id: 'stripe',
    name: 'Stripe',
    icon: stripeIcon,
  },
  {
    id: 'bitpay',
    name: 'BitPay',
    icon: bitpayIcon,
  },
  {
    id: 'wepay',
    name: 'WePay',
    icon: wepayIcon,
  },
]);

// 计算属性
const selectedItems = computed(() => {
  return cartList.value.filter((item) => item.selected);
});

const selectedCount = computed(() => {
  return selectedItems.value.length;
});

const totalCount = computed(() => {
  return cartList.value.filter((item) => item.selected === true).length;
});

// 从GetCheckoutInfo接口获取商品总计
const totalAmount = computed(() => {
  // 如果已经从接口获取了商品总计，则使用接口返回的值
  if (subTotal.value) {
    return subTotal.value;
  }
  // 如果还没有获取到，则使用计算的值作为备用
  const total = selectedItems.value.reduce((sum, item) => {
    return sum + item.price * item.quantity;
  }, 0);
  return `$${total.toFixed(2)}`;
});

// 选中的支付方式图标和名称
const selectedPaymentIcon = computed(() => {
  const payment = paymentMethods.value.find(
    (p) => p.id === selectedPaymentMethod.value
  );
  return payment ? payment.icon : paymentMethods.value[0].icon;
});

const selectedPaymentName = computed(() => {
  const payment = paymentMethods.value.find(
    (p) => p.id === selectedPaymentMethod.value
  );
  return payment ? payment.name : paymentMethods.value[0].name;
});

const currentAddress = ref(null);

// 监听步骤变化
watch(
  () => route.query.step,
  (newStep) => {
    step.value = parseInt(newStep) || 1;
    if (step.value === 2) {
      loadMiniCart();
    }
  }
);

// 监听选中状态变化，更新全选状态
watch(
  cartList,
  () => {
    chooseAll.value =
      cartList.value.length > 0 &&
      cartList.value.every((item) => item.selected);
  },
  { deep: true }
);

// 获取购物车列表
const loadCartList = async () => {
  try {
    loading.value = true;
    const response = await GetCartList();
    console.log('购物车列表:', response);

    if (response && response.carts) {
      cartList.value = response.carts.map((item) => ({
        cart_id: item.cart_id,
        name_format: item.name_format,
        image_url: item.image_url,
        price: item.price,
        price_format: item.price_format,
        quantity: item.quantity,
        stock: item.stock,
        // 根据返回的selected值设置选中状态，如果selected为1则选中
        selected: item.selected === 1,
        sku_id: item.sku_id,
      }));
    }

    // 加载完购物车后，如果有选中的商品，则获取结算信息
    if (cartList.value.some((item) => item.selected)) {
      await loadCheckoutInfo();
    }
  } catch (error) {
    console.error('获取购物车列表失败:', error);
    ElMessage.error('获取购物车列表失败');
  } finally {
    loading.value = false;
  }
};

// 获取迷你购物车信息（第二步用）
const loadMiniCart = async () => {
  try {
    const response = await GetMiniCart();
    console.log('迷你购物车:', response);

    if (response) {
      miniCartData.value = response;
      // 更新选中的商品列表
      if (response.carts) {
        cartList.value = response.carts.map((item) => ({
          cart_id: item.cart_id,
          name_format: item.name_format,
          image_url: item.image_url,
          price: item.price,
          price_format: item.price_format,
          quantity: item.quantity,
          selected: item.selected || false,
        }));
      }
    }
  } catch (error) {
    console.error('获取迷你购物车失败:', error);
    // ElMessage.error('获取迷你购物车失败');
  }
};

// 全选/取消全选
const handleSelectAll = async (checked) => {
  try {
    const cartIds = cartList.value.map((item) => item.cart_id);

    if (checked) {
      await SelectCartItems(cartIds);
      cartList.value.forEach((item) => (item.selected = true));

      // 全选后获取结算信息
      await loadCheckoutInfo();
    } else {
      await UnselectCartItems(cartIds);
      cartList.value.forEach((item) => (item.selected = false));

      // 全部取消选中后，清空商品总计
      subTotal.value = '';
      checkoutInfo.value = null;
    }
  } catch (error) {
    console.error('批量选择失败:', error);
    // ElMessage.error('操作失败');
    chooseAll.value = !checked; // 恢复状态
  }
};

// 选中/取消选中单个商品
const handleSelectItem = async (cartId, checked) => {
  try {
    if (checked) {
      await SelectCartItems([cartId]);
    } else {
      await UnselectCartItems([cartId]);
    }

    const item = cartList.value.find((item) => item.cart_id === cartId);
    if (item) {
      item.selected = checked;
    }

    // 选择商品后重新获取结算信息
    if (cartList.value.some((item) => item.selected)) {
      await loadCheckoutInfo();
    } else {
      // 如果没有选中的商品了，清空商品总计
      subTotal.value = '';
      checkoutInfo.value = null;
    }
  } catch (error) {
    console.error('选择商品失败:', error);
    // ElMessage.error('操作失败');

    // 恢复状态
    const item = cartList.value.find((item) => item.cart_id === cartId);
    if (item) {
      item.selected = !checked;
    }
  }
};

// 更新商品数量
const handleUpdateQuantity = async (cartId, quantity) => {
  try {
    const item = cartList.value.find((item) => item.cart_id === cartId);
    if (!item) return;

    await UpdateCart(cartId, {
      quantity: quantity,
      sku_id: item.sku_id,
    });

    item.quantity = quantity;
    ElMessage.success(t('shu_liang_geng_xin_cheng_gong'));

    // 如果更新的是选中的商品，需要重新获取结算信息
    if (item.selected) {
      await loadCheckoutInfo();
    }
  } catch (error) {
    console.error('更新数量失败:', error);
    // ElMessage.error('更新数量失败');

    // 刷新列表
    loadCartList();
  }
};

// 删除商品
const handleRemoveItem = async (cartId) => {
  try {
    await ElMessageBox.confirm(
      t('que_ding_yao_shan_chu_zhe_ge_shang_pin_ma'),
      t('ti_shi'),
      {
        confirmButtonText: t('que_ding'),
        cancelButtonText: t('qu_xiao'),
        type: 'warning',
      }
    );

    await RemoveCartItem(cartId);
    cartList.value = cartList.value.filter((item) => item.cart_id !== cartId);
    ElMessage.success(t('shang_pin_yi_shan_chu'));

    // 删除商品后重新获取结算信息
    if (cartList.value.some((item) => item.selected)) {
      await loadCheckoutInfo();
    } else {
      // 如果没有选中的商品了，清空商品总计
      subTotal.value = '';
      checkoutInfo.value = null;
    }
  } catch (error) {
    if (error === 'cancel') return;

    console.error('删除商品失败:', error);
    // ElMessage.error('删除商品失败');
  }
};

// 获取结算信息
const loadCheckoutInfo = async () => {
  try {
    const response = await GetCheckoutInfo();
    console.log('结算页信息:', response);

    checkoutInfo.value = response;

    // 从totals数组中找到code为sub_total的项，获取商品总计
    if (response && response.totals && Array.isArray(response.totals)) {
      const subTotalItem = response.totals.find(
        (item) => item.code === 'sub_total'
      );
      if (subTotalItem) {
        subTotal.value = subTotalItem.amount_format;
        console.log('商品总计:', subTotal.value);
      }
    }

    return response;
  } catch (error) {
    console.error('获取结算信息失败:', error);
    return null;
  }
};

// 下一步
const handleNext = () => {
  if (step.value === 1) {
    if (selectedCount.value === 0) {
      ElMessage.warning(t('qing_xuan_ze_shang_pin'));
      return;
    }
    router.push({ path: '/confirm', query: { step: 2 } });
  } else if (step.value === 2) {
    handleSubmitOrder();
  }
};

// 提交订单
const handleSubmitOrder = async () => {
  try {
    loading.value = true;
    const response = await ConfirmOrder();
    console.log('订单提交结果:', response);

    if (response && response.number) {
      paymentInfo.value = { orderNumber: response.number, ...response };
      router.push({ path: '/confirm', query: { step: 3 } });

      GetOrderPayInfo(response.number).then((res) => {
        console.log('支付信息', res);
        payInfo.value = res.payment_setting;
      });
    }
  } catch (error) {
    console.error('提交订单失败:', error);
    // ElMessage.error('提交订单失败');
  } finally {
    loading.value = false;
  }
};

// 处理支付
const handlePayment = async () => {
  try {
    const orderNumber = paymentInfo.value.orderNumber;
    const amount = parseFloat(paymentInfo.value.total);

    if (!orderNumber) {
      ElMessage.error(t('ding_dan_xin_xi_bu_wan_zheng'));
      return;
    }

    // 根据选中的支付方式进行支付
    await processPayment(selectedPaymentMethod.value, orderNumber, amount);
  } catch (error) {
    console.error('支付处理失败:', error);
    // ElMessage.error('支付处理失败');
  }
};

// 加载地址列表
const loadAddressList = async () => {
  try {
    const response = await GetAddressList();
    console.log('地址列表:', response);

    if (response && response.addresses) {
      addressList.value = response.addresses.map((address) => ({
        id: address.id,
        name: address.name,
        phone: address.phone,
        full_address: address.full_address,
        is_default: address.is_default,
      }));

      // 设置默认选中地址
      const defaultAddress = addressList.value.find((addr) => addr.is_default);
      if (defaultAddress) {
        selectedAddressId.value = defaultAddress.id;
      } else if (addressList.value.length > 0) {
        selectedAddressId.value = addressList.value[0].id;
      }
    }
  } catch (error) {
    // console.error('获取地址列表失败:', error);
    // ElMessage.error('获取地址列表失败');
  }
};

// 选择地址
const handleSelectAddress = (addressId) => {
  selectedAddressId.value = addressId;
};

// 添加地址
const handleAddAddress = () => {
  editAddressId.value = null;
  currentAddress.value = null;
  addressDialogVisible.value = true;
};

// 编辑地址
const handleEditAddress = async (address) => {
  try {
    loading.value = true;
    editAddressId.value = address.id;

    // 获取完整的地址详情
    const addressDetail = await GetAddressDetail(address.id);
    console.log('获取到的地址详情:', addressDetail);

    // 设置完整的地址信息到currentAddress
    currentAddress.value = addressDetail || { ...address };
    addressDialogVisible.value = true;
  } catch (error) {
    console.error('获取地址详情失败:', error);
    // ElMessage.error('获取地址详情失败');
  } finally {
    loading.value = false;
  }
};

// 删除地址
const handleDeleteAddress = async (addressId) => {
  try {
    await ElMessageBox.confirm(
      t('que_ding_yao_shan_chu_zhe_ge_di_zhi_ma'),
      t('ti_shi'),
      {
        confirmButtonText: t('que_ding'),
        cancelButtonText: t('qu_xiao'),
        type: 'warning',
      }
    );

    await DeleteAddress(addressId);
    ElMessage.success(t('di_zhi_yi_shan_chu'));

    // 重新加载地址列表
    loadAddressList();

    // 如果删除的是当前选中的地址，重置选中状态
    if (selectedAddressId.value === addressId) {
      selectedAddressId.value = null;
    }
  } catch (error) {
    if (error === 'cancel') return;

    console.error('删除地址失败:', error);
    // ElMessage.error('删除地址失败');
  }
};

// 地址操作成功回调
const handleAddressSuccess = () => {
  loadAddressList();
};

// 选择支付方式
const handleSelectPayment = (paymentId) => {
  selectedPaymentMethod.value = paymentId;
  UpdateCheckout({
    payment_method_code: paymentId,
  });
  console.log('选择支付方式:', paymentId);
};

// 处理具体的支付流程
const processPayment = async (paymentMethod, orderNumber, amount) => {
  try {
    switch (paymentMethod) {
      case 'paypal':
        await handlePayPalPayment(orderNumber, amount);
        break;
      case 'stripe':
        router.push(`/pay?order_number=${orderNumber}`);

        // await handleStripePayment(orderNumber, amount);
        break;
      case 'bitpay':
        await handleBitPayPayment(orderNumber, amount);
        break;
      case 'wepay':
        await handleWePayPayment(orderNumber, amount);
        break;
      default:
        throw new Error(
          `${t('bu_zhi_chi_de_zhi_fu_fang_shi')} ${paymentMethod}`
        );
    }
  } catch (error) {
    console.error(`${paymentMethod}支付失败:`, error);
    throw error;
  }
};

// PayPal支付处理
const handlePayPalPayment = async (orderNumber, amount) => {
  try {
    const paypal = PaymentFactory.createPayment('paypal', {
      clientId: payInfo.value.live_client_id,
      ...payInfo.value,
    });

    // 创建PayPal支付按钮容器
    const containerId = 'paypal-payment-container';
    let container = document.getElementById(containerId);

    if (!container) {
      container = document.createElement('div');
      container.id = containerId;
      container.style.cssText = `
                position: fixed;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                background: white;
                padding: 20px;
                border-radius: 8px;
                box-shadow: 0 4px 12px rgba(0,0,0,0.3);
                z-index: 9999;
                min-width: 300px;
            `;
      document.body.appendChild(container);
    }

    container.innerHTML = `
            <div id="paypal-button-container"></div>
            <button onclick="this.parentElement.remove()" style="margin-top: 10px; padding: 8px 16px; background: #ccc; border: none; border-radius: 4px; cursor: pointer;width:100%">取消</button>
        `;

    const result = await paypal.createPayment(amount, orderNumber);

    if (result && result.success) {
      // 调用后端API确认支付
      // await PayWithPayPal(orderNumber, {
      //     paypalOrderId: result.orderId,
      //     paymentId: result.paymentId
      // });

      ElMessage.success(t('paypal_zhi_fu_cheng_gong'));
      container.remove();
      router.push('/order');
    }
  } catch (error) {
    console.error('PayPal支付失败:', error);
    // ElMessage.error('PayPal支付失败');
    throw error;
  }
};

// Stripe支付处理
const handleStripePayment = async (orderNumber, amount) => {
  try {
    const stripe = PaymentFactory.createPayment('stripe', {
      publishableKey: payInfo.value.publishable_key,
      ...payInfo.value,
    });

    ElMessage.info(t('zheng_zai_chu_shi_hua_stripe_zhi_fu'));

    const result = await stripe.createPayment(amount, orderNumber);

    if (result && result.success) {
      // 调用后端API确认支付
      // await PayWithStripe(orderNumber, {
      //     paymentIntentId: result.paymentIntentId
      // });

      ElMessage.success(t('stripe_zhi_fu_cheng_gong'));
      router.push('/order');
    }
  } catch (error) {
    console.error('Stripe支付失败:', error);
    // ElMessage.error('Stripe支付失败');
    throw error;
  }
};

// BitPay支付处理
const handleBitPayPayment = async (orderNumber, amount) => {
  try {
    const bitpay = PaymentFactory.createPayment('bitpay', payInfo.value);

    const result = await bitpay.createPayment(amount, orderNumber);

    if (result && result.success) {
      // 调用后端API记录支付信息
      // await PayWithBitPay(orderNumber, {
      //     invoiceId: result.invoiceId,
      //     paymentUrl: result.paymentUrl
      // });

      ElMessage.success(
        t(
          'bitpay_zhi_fu_lian_jie_yi_da_kai_qing_zai_xin_chuang_kou_zhong_wan_cheng_zhi_fu'
        )
      );

      // 开始轮询支付状态
      pollPaymentStatus(orderNumber, 'bitpay');
    }
  } catch (error) {
    console.error('BitPay支付失败:', error);
    // ElMessage.error('BitPay支付失败');
    throw error;
  }
};

// 微信支付处理
const handleWePayPayment = async (orderNumber, amount) => {
  try {
    const wepay = PaymentFactory.createPayment('wepay', payInfo.value);

    const result = await wepay.createPayment(amount, orderNumber);

    if (result && result.success) {
      // 调用后端API记录支付信息
      // await PayWithWePay(orderNumber, {
      //     prepayId: result.prepayId,
      //     codeUrl: result.codeUrl
      // });

      ElMessage.success(
        t('wei_xin_zhi_fu_er_wei_ma_yi_sheng_cheng_qing_sao_ma_zhi_fu')
      );

      // 开始轮询支付状态
      pollPaymentStatus(orderNumber, 'wepay');
    }
  } catch (error) {
    console.error('微信支付失败:', error);
    // ElMessage.error('微信支付失败');
    throw error;
  }
};

// 轮询支付状态
const pollPaymentStatus = async (
  orderNumber,
  paymentMethod,
  maxAttempts = 60
) => {
  let attempts = 0;

  // const poll = async () => {
  //     try {
  //         attempts++;
  //         const status = await GetPaymentStatus(orderNumber, paymentMethod);

  //         if (status.paid) {
  //             ElMessage.success('支付成功！');
  //             router.push('/order');
  //             return;
  //         }

  //         if (status.failed) {
  //             ElMessage.error('支付失败');
  //             return;
  //         }

  //         if (attempts < maxAttempts) {
  //             setTimeout(poll, 3000); // 每3秒检查一次
  //         } else {
  //             ElMessage.warning('支付状态检查超时，请手动刷新页面查看订单状态');
  //         }
  //     } catch (error) {
  //         console.error('检查支付状态失败:', error);
  //         if (attempts < maxAttempts) {
  //             setTimeout(poll, 3000);
  //         }
  //     }
  // };

  // poll();
};

onMounted(async () => {
  if (step.value === 1) {
    await loadCartList();
  } else if (step.value === 2) {
    await loadMiniCart();
    await loadAddressList();

    // 在第二步加载时获取结算信息
    if (cartList.value.filter((item) => item.selected).length > 0) {
      const checkoutInfoResponse = await loadCheckoutInfo();

      if (checkoutInfoResponse && checkoutInfoResponse.payment_methods) {
        // 定义本地支持的支付方式
        const localPaymentMethods = [
          {
            id: 'paypal',
            name: 'PayPal',
            icon: paypalIcon,
          },
          {
            id: 'stripe',
            name: 'Stripe',
            icon: stripeIcon,
          },
          {
            id: 'bitpay',
            name: 'BitPay',
            icon: bitpayIcon,
          },
          {
            id: 'wechat',
            name: 'WePay',
            icon: wepayIcon,
          },
        ];

        // 筛选本地支持的支付方式，只保留接口返回的方式
        const filteredMethods = localPaymentMethods.filter((localMethod) =>
          checkoutInfoResponse.payment_methods.some(
            (apiMethod) => apiMethod.code === localMethod.id
          )
        );

        // 更新支付方式列表
        paymentMethods.value = filteredMethods;

        // 如果当前选中的支付方式不在筛选后的列表中，则选择第一个可用的支付方式
        if (
          filteredMethods.length > 0 &&
          !filteredMethods.some(
            (method) => method.id === selectedPaymentMethod.value
          )
        ) {
          selectedPaymentMethod.value = filteredMethods[0].id;
        }
      }
    }
  }
});
</script>
<style lang="scss" scoped>
@import url('./index.scss');
@import url('./index.mobile.scss');

.loading-state,
.empty-state {
  text-align: center;
  padding: 2rem;
  color: #666;
  font-size: 1.4rem;
}

.cart_view_box_btn.disabled {
  opacity: 0.5;
  cursor: not-allowed;
  pointer-events: none;
}

/* 地址相关样式 */
.cart_view_box_address {
  position: relative;
  border: 1px solid #e0e0e0;
  border-radius: 0.8rem;
  padding: 1.5rem;
  margin-bottom: 1rem;
  cursor: pointer;
  transition: all 0.2s;

  &:hover {
    border-color: #6e4aeb;
    box-shadow: 0 2px 8px rgba(110, 74, 235, 0.1);
  }

  &.selected {
    border-color: #6e4aeb;
    background: rgba(110, 74, 235, 0.05);
  }

  .cart_view_box_address_user {
    font-size: 1.8rem;
    font-weight: 600;
    color: #303133;
    margin-bottom: 0.5rem;

    .default-tag {
      display: inline-block;
      padding: 0.2rem 0.6rem;
      background: #6e4aeb;
      color: white;
      font-size: 1.2rem;
      border-radius: 0.3rem;
      margin-left: 1rem;
    }
  }

  .cart_view_box_address_detail {
    font-size: 1.3rem;
    color: #666;
    line-height: 1.4;
  }

  .cart_view_box_address_action {
    display: flex;
    gap: 0.8rem;

    .cart_view_box_address_action_icon {
      width: 1.8rem;
      height: 1.8rem;
      cursor: pointer;
      opacity: 0.6;
      transition: opacity 0.2s;

      &:hover {
        opacity: 1;
      }
    }
  }
}

.cart_view_box_add {
  border: 1px dashed #d0d0d0;
  border-radius: 0.8rem;
  padding: 0 2rem;
  text-align: center;
  cursor: pointer;
  transition: all 0.2s;
  color: #666;
  font-size: 1.6rem;

  &:hover {
    border-color: #6e4aeb;
    color: #6e4aeb;
  }

  .cart_view_box_add_img {
    width: 2rem;
    height: 2rem;
    margin-right: 0.8rem;
    vertical-align: middle;
  }
}

.empty-address {
  text-align: center;
  padding: 3rem;
  color: #999;
  font-size: 1.4rem;

  p {
    margin: 0;
  }
}

/* 支付方式样式 */
.cart_view_box_payment {
  display: flex;
  gap: 1.5rem;
  margin-bottom: 2rem;
  flex-wrap: wrap;

  .cart_view_box_payment_item {
    position: relative;
    border: 2px solid #e0e0e0;
    border-radius: 0.8rem;
    padding: 1rem;
    cursor: pointer;
    transition: all 0.3s ease;
    background: white;
    display: flex;
    align-items: center;
    justify-content: center;
    min-width: 8rem;
    height: 5rem;

    &:hover {
      border-color: #6e4aeb;
      box-shadow: 0 2px 8px rgba(110, 74, 235, 0.1);
      transform: translateY(-2px);
    }

    &.selected {
      border-color: #6e4aeb;
      background: rgba(110, 74, 235, 0.05);
      box-shadow: 0 4px 12px rgba(110, 74, 235, 0.2);

      &::after {
        content: '✓';
        position: absolute;
        top: -0.5rem;
        right: -0.5rem;
        width: 2rem;
        height: 2rem;
        background: #6e4aeb;
        color: white;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.2rem;
        font-weight: bold;
      }
    }

    .cart_view_box_payment_item_icon {
      max-width: 100%;
      max-height: 100%;
      object-fit: contain;
    }
  }
}

/* 移动视图下支付页面的特定样式 */
.cart_view_pay.mobile-view {
  padding: 2rem 1.5rem;
  flex-direction: column;
  align-items: center;

  .cart_view_pay_img {
    margin-bottom: 2rem;
  }

  .cart_view_pay_main {
    width: 100%;

    &_title {
      text-align: center;
      margin-bottom: 1.5rem;
    }

    &_info {
      padding: 1.5rem;
      border-radius: 0.8rem;

      &_item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        font-size: 1.4rem;

        img.payment-icon {
          max-height: 2.5rem;
          max-width: 7rem;
        }
      }
    }

    &_action {
      flex-direction: column;

      &_item {
        width: 100%;
        margin-right: 0;
        margin-bottom: 1rem;
      }
    }
  }
}

/* 支付页面第三步响应式样式 */
.cart_view_pay {
  @media (max-width: 768px) {
    flex-direction: column;
    padding: 1.5rem;

    .cart_view_pay_img {
      width: 6rem;
      height: 6rem;
      margin: 0 auto 1.5rem;
    }

    .cart_view_pay_main {
      width: 100%;
      padding: 0;

      &_title {
        font-size: 1.8rem;
        text-align: center;
        margin-bottom: 2rem;
      }

      &_info {
        padding: 1.5rem;
        border-radius: 0.8rem;

        &_item {
          font-size: 1.4rem;
          margin-bottom: 1.2rem;
          display: flex;
          flex-wrap: wrap;
          justify-content: space-between;

          .red {
            font-size: 1.6rem;
          }

          .payment-icon {
            max-height: 2.4rem;
            max-width: 8rem;
          }
        }
      }

      &_action {
        margin-top: 2rem;

        &_item {
          width: 100%;
          height: 4.5rem;
          border-radius: 2.3rem;
          font-size: 1.6rem;

          img {
            width: 1.8rem;
            height: 1.8rem;
          }
        }
      }
    }
  }
}
</style>

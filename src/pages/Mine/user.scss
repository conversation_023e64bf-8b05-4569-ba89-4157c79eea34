.user_box {
  height: 69rem;
  background: #ffffff;
  border-radius: 2rem;
  padding: 3rem 2.4rem;
  box-sizing: border-box;
  &_title {
    display: flex;
    align-items: center;
    height: 2.8rem;
    font-family: PingFang SC, PingFang SC;
    font-weight: 400;
    font-size: 2rem;
    color: #242426;
    margin-bottom: 3rem;
  }
  &_title::before {
    content: '';
    // background: #fd560f;
    height: 100%;
    width: 2px;
    position: absolute;
    left: 0;
    top: 0;
  }
  &_line {
    width: 100%;
    height: 0;
    border: 0.1rem solid #e7e7e7;
    margin-bottom: 4rem;
  }
  &_main {
    display: flex;
    &_avator {
      width: 12rem;
      height: 12rem;
      border-radius: 50%;
      margin-bottom: 4rem;
    }
    &_form {
      box-sizing: border-box;
      flex: 1;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      &_upload {
        width: 32rem;
        height: 5.2rem;
        background: #f6f6f6;
        border-radius: 3.2rem;
        border: 0.1rem solid #e7e7e7;
        display: flex;
        align-items: center;
        justify-content: center;
        font-family: PingFang SC, PingFang SC;
        font-weight: 400;
        font-size: 1.6rem;
        color: #242426;
        cursor: pointer;
        &_icon {
          width: 1.8rem;
          height: 1.8rem;
          margin-left: 1.2rem;
        }
      }
      &_tip {
        margin-top: 1.6rem;
        height: 2rem;
        line-height: 2rem;
        font-family: PingFang SC, PingFang SC;
        font-weight: 400;
        font-size: 1.4rem;
        color: #70707b;
        margin-bottom: 2.4rem;
      }
      &_control {
        width: 32rem;
        height: 5.2rem;
        background: #f6f6f6;
        border-radius: 3.2rem;
        margin-bottom: 2rem;
        &_val {
          width: 32rem;
          height: 5.2rem;
          line-height: 5.2rem;
          font-family: PingFang SC, PingFang SC;
          font-weight: 400;
          font-size: 1.6rem;
          background: transparent;
          border: none;
          color: #70707b;
          text-indent: 2.4rem;
        }
      }
      &_control:last-child {
        margin-bottom: 4.2rem;
      }
      &_btn {
        width: 32rem;
        height: 4.8rem;
        background: #6e4aeb;
        border-radius: 3.2rem;
        display: flex;
        align-items: center;
        justify-content: center;
        font-family: PingFang SC, PingFang SC;
        font-weight: 600;
        font-size: 1.8rem;
        cursor: pointer;
        color: #ffffff;
      }
    }
  }
  &_avatar {
    display: flex;
    flex-direction: column;
    margin-bottom: 20px;
    p {
      margin-top: 10px;
    }
  }
}

/* 移动端特定的样式 */
@media screen and (max-width: 768px) {
  .center_box {
    padding: 0 0 8rem 0; /* 增加底部边距，避免内容被截断 */
    background-color: #f5f5f5;
    min-height: 100vh; /* 确保至少有视窗高度 */
    height: auto; /* 允许高度自动增长 */
    overflow-y: visible; /* 确保可以滚动查看全部内容 */

    /* 移动端用户信息区域样式优化 */
    .mobile_user_info {
      margin-bottom: 1.2rem;
      padding: 1.5rem;
      background-color: #f5f5f5;
      position: relative;

      &_header {
        display: flex;
        align-items: center;
        justify-content: space-between;
      }

      &_avatar {
        width: 5rem;
        height: 5rem;
        border-radius: 50%;
        object-fit: cover;
      }

      &_details {
        flex: 1;
        padding: 0 1rem;
      }

      &_name {
        margin: 0 0 0.3rem 0;
        font-size: 1.6rem;
        font-weight: 500;
      }

      &_email {
        margin: 0;
        font-size: 1.3rem;
        color: #999;
      }

      &_route_name {
        display: flex;
        align-items: center;
        font-size: 1.4rem;
        font-weight: 500;
        color: #333;

        &_text {
          margin-right: 0.5rem;
        }

        &_icon {
          width: 1.2rem;
          height: 1.2rem;
          transition: transform 0.3s;

          &.active {
            transform: rotate(180deg);
          }
        }
      }

      .mobile_menu_dropdown {
        position: absolute;
        top: 100%;
        right: 0;
        width: 100%;
        background-color: #fff;
        box-shadow: 0 0.4rem 1rem rgba(0, 0, 0, 0.1);
        border-radius: 0 0 0.5rem 0.5rem;
        z-index: 10;
        transform: translateY(-100%);
        opacity: 0;
        visibility: hidden;
        transition: all 0.3s ease;

        &.active {
          transform: translateY(0);
          opacity: 1;
          visibility: visible;
        }

        &_item {
          padding: 1.2rem 1.5rem;
          display: flex;
          align-items: center;
          border-bottom: 1px solid #f0f0f0;
          font-size: 1.4rem;
          color: #333;

          .mine_box_menu_item_icon {
            width: 2rem;
            height: 2rem;
            margin-right: 1.2rem;
          }

          span {
            font-size: 1.6rem;
          }

          &:last-child {
            border-bottom: none;
          }

          &.active {
            color: #6e4aeb;
            font-weight: 500;
          }

          /* 退出登录按钮的特殊样式 */
          &[style*='text-align: center'] {
            justify-content: center;

            span {
              color: #ff4d4f;
              font-weight: 500;
            }

            .mine_box_menu_item_icon {
              margin-right: 1.5rem;
            }
          }
        }
      }

      &_arrow {
        img {
          width: 1.4rem;
          height: 1.4rem;
        }
      }
    }

    // 订单卡片标题区样式
    .center_box_section_title {
      font-size: 1.5rem;
      padding: 0 0.5rem;
    }

    // 订单列表容器样式
    .center_box_section_list {
      max-height: unset !important;
      height: auto !important;
      overflow-y: visible !important;
      padding-bottom: 2rem; // 添加底部间距
    }

    // 订单卡片样式
    .center_box_section_list_item {
      margin-bottom: 1rem;
    }

    .order_card {
      flex-direction: row;
      align-items: center;
      padding: 1.5rem;
      border-radius: 0.8rem;
      border: none;
      background-color: #fff;
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);

      &_image {
        margin-right: 1rem;
        margin-bottom: 0;

        .product_img {
          width: 6rem;
          height: 6rem;
          border-radius: 0.4rem;
          object-fit: contain;
          border: 1px solid #f0f0f0;
        }
      }

      &_content {
        width: auto;
        flex: 1;
        margin-bottom: 0;
        padding-right: 1rem;

        .order_info_line {
          flex-direction: column;
          align-items: flex-start;
          margin-bottom: 0.4rem;

          .order_label {
            display: none; /* 隐藏标签，只显示值 */
          }

          .order_value {
            font-size: 1.3rem;
            color: #333;
          }

          .product_line {
            display: none; /* 隐藏分隔线 */
          }

          .product_count {
            font-size: 1.3rem;
            color: #999;
            margin-top: 0.2rem;
          }
        }
      }

      &_status {
        display: flex;
        flex-direction: column;
        align-items: flex-end;
        justify-content: space-between;
        min-height: 6rem; /* 改为最小高度，允许内容自动增长 */
        height: auto;
        width: auto;

        .status_badge {
          font-size: 1.3rem;
          padding: 0.3rem 0.8rem;
          background-color: #fff8e6;
          color: #ff9500;
          border-radius: 2rem;
        }

        .order_price {
          margin: 0;
          font-size: 1.6rem;
          font-weight: bold;
          color: #333;
        }

        .view_detail {
          font-size: 1.3rem;
          padding: 0.3rem 0.8rem;
          background-color: #f5f5f5;
          border-radius: 2rem;
          color: #666;
        }
      }
    }

    &_section_list_item {
      margin-bottom: 1.5rem;
    }

    .empty_orders {
      padding: 3rem 0;
      text-align: center;
      font-size: 1.4rem;
      color: #999;
    }
  }
}

<template>
  <div class="pwd_box">
    <div class="pwd_box_title">{{ t('center.pwd') }}</div>
    <div class="pwd_box_line"></div>
    <div class="pwd_box_form" v-loading="loading">
      <div class="pwd_box_form_control">
        <input
          v-model="passwordForm.current_password"
          class="pwd_box_form_control_val"
          :placeholder="t('center.placeholder1')"
          type="password"
        />
      </div>
      <div class="pwd_box_form_control">
        <input
          v-model="passwordForm.new_password"
          class="pwd_box_form_control_val"
          :placeholder="t('center.placeholder2')"
          type="password"
        />
      </div>
      <div class="pwd_box_form_control">
        <input
          v-model="passwordConfirm"
          class="pwd_box_form_control_val"
          :placeholder="t('center.placeholder3')"
          type="password"
        />
      </div>
      <div class="pwd_box_form_btn" @click="onSubmit" :disabled="submitLoading">
        {{ submitLoading ? t('ti_jiao_zhong') : t('center.submit') }}
      </div>
    </div>
  </div>
</template>
<script setup>
import { ref, reactive } from 'vue';
import { useRouter } from 'vue-router';
import { useI18n } from 'vue-i18n';
import { ElMessage } from 'element-plus';
import { UpdatePassword } from '@api/account';

const { t, locale } = useI18n();
const router = useRouter();

const loading = ref(false);
const submitLoading = ref(false);
const passwordConfirm = ref('');

const passwordForm = reactive({
  current_password: '',
  new_password: '',
});

// 表单验证
const validateForm = () => {
  if (!passwordForm.current_password) {
    ElMessage.error(t('qing_shu_ru_dang_qian_mi_ma'));
    return false;
  }

  if (!passwordForm.new_password) {
    ElMessage.error(t('qing_shu_ru_xin_mi_ma'));
    return false;
  }

  if (passwordForm.new_password.length < 6) {
    ElMessage.error(t('xin_mi_ma_chang_du_bu_neng_shao_yu_6_wei'));
    return false;
  }

  if (!passwordConfirm.value) {
    ElMessage.error(t('qing_que_ren_xin_mi_ma'));
    return false;
  }

  if (passwordForm.new_password !== passwordConfirm.value) {
    ElMessage.error(t('liang_ci_shu_ru_de_xin_mi_ma_bu_yi_zhi'));
    return false;
  }

  if (passwordForm.current_password === passwordForm.new_password) {
    ElMessage.error(t('xin_mi_ma_bu_neng_yu_dang_qian_mi_ma_xiang_tong'));
    return false;
  }

  return true;
};

// 提交修改密码
const onSubmit = async () => {
  if (!validateForm()) {
    return;
  }

  try {
    submitLoading.value = true;

    const data = {
      old_password: passwordForm.current_password,
      new_password: passwordForm.new_password,
      new_password_confirmation: passwordConfirm.value,
    };

    await UpdatePassword(data);
    ElMessage.success(t('mi_ma_xiu_gai_cheng_gong'));

    // 清空表单
    passwordForm.current_password = '';
    passwordForm.new_password = '';
    passwordConfirm.value = '';
  } catch (error) {
    console.error('修改密码失败:', error);
    // const message = error.response?.data?.message || '密码修改失败';
    // ElMessage.error(message);
  } finally {
    submitLoading.value = false;
  }
};
</script>
<style lang="scss" scoped>
@import url('./pwd.scss');

.pwd_box_form_btn {
  cursor: pointer;
  transition: opacity 0.2s;

  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }
}
</style>

.box {
  min-height: 69rem;
  background: #ffffff;
  border-radius: 2rem;
  padding: 3rem 2.4rem;
  box-sizing: border-box;
  &_title {
    display: flex;
    align-items: center;
    height: 2.8rem;
    font-family: PingFang SC, PingFang SC;
    font-weight: 400;
    font-size: 2rem;
    color: #242426;
    margin-bottom: 3rem;
    position: relative;

    &_back {
      margin-right: 1.6rem;
      width: 2.4rem;
      height: 2.4rem;
      cursor: pointer;

      &:hover {
        opacity: 0.8;
      }
    }
  }
  &_thead {
    display: flex;
    align-items: center;
    height: 3.6rem;
    background: #f6f6f6;
    &_th {
      font-size: 1.6rem;
    }
  }
  &_tbody {
    min-height: 53.6rem;
    overflow-y: auto;
  }
  &_tr {
    display: flex;
    padding: 2.4rem 0;
    cursor: pointer;
    border-bottom: 0.1rem solid #e7e7e7;
    &_td {
      display: flex;
      align-items: center;
      font-family: PingFang SC, PingFang SC;
      font-weight: 600;
      font-size: 1.8rem;
      color: #242426;
      &_img {
        width: 10rem;
        height: 10rem;
        margin-right: 1.6rem;
      }
    }
    &_td.del {
      font-size: 1.8rem;
      cursor: pointer;
      color: #6e4aeb;
    }
  }
}
.w383 {
  width: 38.3rem;
  padding-left: 2.4rem;
  box-sizing: border-box;
}
.w149 {
  width: 14.9rem;
}
.w154 {
  width: 15.4rem;
}
.w202 {
  width: 20.2rem;
}
.w92 {
  width: 9.2rem;
  font-size: 1.8rem;
}

.empty_state {
  text-align: center;
  padding: 4rem;
  color: #999;
  font-size: 1.4rem;
}

.pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-top: 2rem;
  gap: 1rem;

  .page_btn {
    padding: 0.8rem 1.6rem;
    background: #f6f6f6;
    border: 0.1rem solid #e7e7e7;
    border-radius: 0.4rem;
    cursor: pointer;
    font-size: 1.4rem;
    color: #242426;

    &:hover:not(:disabled) {
      background: #6e4aeb;
      color: #ffffff;
      border-color: #6e4aeb;
    }

    &:disabled {
      background: #f0f0f0;
      color: #ccc;
      cursor: not-allowed;
    }
  }

  .page_info {
    font-size: 1.4rem;
    color: #242426;
    margin: 0 1rem;
  }
}

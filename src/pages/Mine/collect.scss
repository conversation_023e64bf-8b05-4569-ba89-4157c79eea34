.collect_box {
  min-height: 69rem;
  background: #ffffff;
  border-radius: 2rem;
  padding: 3rem 2.4rem;
  box-sizing: border-box;
  &_title {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 2.8rem;
    font-family: PingFang SC, PingFang SC;
    font-weight: 400;
    font-size: 2rem;
    color: #242426;
    margin-bottom: 3rem;
  }
  &_thead {
    display: flex;
    align-items: center;
    height: 3.6rem;
    background: #f6f6f6;
    &_th {
      font-size: 1.6rem;
    }
  }
  &_tbody {
    height: 53.6rem;
    overflow-y: auto;
  }
  &_tr {
    display: flex;
    padding: 2.4rem 0;
    border-bottom: 0.1rem solid #e7e7e7;
    cursor: pointer;
    &_td {
      display: flex;
      align-items: center;
      font-family: PingFang SC, PingFang SC;
      font-weight: 600;
      font-size: 1.6rem;
      color: #242426;
      &_img {
        width: 10rem;
        height: 10rem;
        margin-right: 1.6rem;
      }
    }
    &_td.price {
      font-size: 1.8rem;
    }
  }
}
.w700 {
  width: 70rem;
  padding-left: 2.4rem;
  box-sizing: border-box;
}
.w280 {
  width: 28rem;
}

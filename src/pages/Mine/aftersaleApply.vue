<template>
  <div class="aftersale_apply_container" v-loading="loading">
    <div class="header">
      <img src="@assets/back.png" class="back_btn" @click="goBack" />
      <h1 class="title">{{ t('shen_qing_shou_hou') }}</h1>
    </div>

    <div v-if="productInfo" class="content">
      <!-- 产品信息 -->
      <div class="product_info">
        <div class="product_image">
          <img :src="getProductImage(productInfo)" :alt="productInfo.name" />
        </div>
        <div class="product_details">
          <h3 class="product_name">{{ productInfo.name || 'Ledger Flex' }}</h3>
          <div class="product_quantity">x {{ productInfo.quantity || 1 }}</div>
        </div>
      </div>

      <!-- 售后信息表单 -->
      <div class="form_section">
        <div class="section_title">
          <div class="title_bar"></div>
          <span>{{ t('tian_xie_shou_hou_xin_xi') }}</span>
        </div>

        <div class="form_group">
          <label class="form_label">{{ t('center.type') }}</label>
          <div class="form_control">
            <el-select
              v-model="formData.type"
              :placeholder="t('qing_xuan_ze')"
              class="full_width"
            >
              <el-option
                v-for="(label, value) in apiData.types"
                :key="value"
                :label="label"
                :value="value"
              />
            </el-select>
          </div>
        </div>

        <div class="form_row">
          <div class="form_group half">
            <label class="form_label"
              >{{ t('tui_huan_shu_liang') }}
              <span class="required">*</span></label
            >
            <div class="form_control">
              <el-input-number
                v-model="formData.quantity"
                :min="1"
                :max="productInfo.quantity"
                class="full_width"
              />
            </div>
          </div>
          <div class="form_group half">
            <label class="form_label">{{ t('tui_huo_yuan_yin') }}</label>
            <div class="form_control">
              <el-select
                v-model="formData.reason"
                :placeholder="t('qing_xuan_ze')"
                class="full_width"
              >
                <el-option
                  v-for="reason in apiData.reasons"
                  :key="reason.id"
                  :label="reason.name"
                  :value="reason.id"
                />
              </el-select>
            </div>
          </div>
        </div>

        <div class="form_group">
          <label class="form_label">{{ t('yi_da_kai_bao_zhuang') }}</label>
          <div class="form_control">
            <el-select
              v-model="formData.opened"
              :placeholder="t('qing_xuan_ze')"
              class="full_width"
            >
              <el-option :label="t('fou')" value="0" />
              <el-option :label="t('shi')" value="1" />
            </el-select>
          </div>
        </div>

        <!-- 图片上传 -->
        <div class="form_group">
          <label class="form_label">{{ t('tu_pian') }}</label>
          <div class="upload_area">
            <div class="uploaded_images">
              <div
                v-for="(image, index) in uploadedImages"
                :key="index"
                class="image_item"
              >
                <img :src="image.url" :alt="`上传图片${index + 1}`" />
                <div class="remove_btn" @click="removeImage(index)">×</div>
              </div>
            </div>
            <div
              class="upload_btn"
              @click="triggerFileUpload"
              v-if="uploadedImages.length < 5"
            >
              <div class="upload_icon">+</div>
            </div>
            <input
              ref="fileInput"
              type="file"
              accept="image/*"
              multiple
              style="display: none"
              @change="handleFileUpload"
            />
          </div>
        </div>

        <!-- 备注 -->
        <div class="form_group">
          <label class="form_label">{{ t('bei_zhu') }}</label>
          <div class="form_control">
            <el-input
              v-model="formData.comment"
              type="textarea"
              :rows="4"
              :placeholder="t('qing_shu_ru')"
              class="full_width"
            />
          </div>
        </div>
      </div>

      <!-- 提交按钮 -->
      <div class="submit_section">
        <button
          class="submit_btn"
          @click="submitAftersale"
          :disabled="submitting"
        >
          {{ submitting ? t('ti_jiao_zhong') : t('center.submit') }}
        </button>
      </div>
    </div>

    <div v-else-if="!loading" class="empty_state">
      <p>{{ t('chan_pin_xin_xi_jia_zai_shi_bai') }}</p>
      <div class="retry_btn" @click="loadProductInfo">
        {{ t('zhong_xin_jia_zai') }}
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import { ElMessage } from 'element-plus';
import { CreateRMAPage, SubmitRMA, UploadFile } from '@api/account';
import { useI18n } from 'vue-i18n';
const { t } = useI18n();

const router = useRouter();
const route = useRoute();

const loading = ref(false);
const submitting = ref(false);
const productInfo = ref({});
const uploadedImages = ref([]);
const fileInput = ref(null);
const apiData = ref({
  types: {},
  reasons: [],
  statuses: {},
});

const formData = ref({
  type: 'return',
  quantity: 1,
  opened: '0',
  reason: '',
  comment: '',
  images: [],
});

// 获取产品信息
const loadProductInfo = async () => {
  const orderProductId = route.query.order_product_id;
  if (!orderProductId) {
    ElMessage.error(t('que_shao_chan_pin_xin_xi'));
    router.back();
    return;
  }

  try {
    loading.value = true;
    const response = await CreateRMAPage(orderProductId);
    console.log('产品信息:', response);

    if (response.orderProduct) {
      productInfo.value = response.orderProduct;
      formData.value.quantity = 1;
    }

    // 保存API返回的选项数据
    if (response.types) {
      apiData.value.types = response.types;
    }
    if (response.reasons) {
      apiData.value.reasons = response.reasons;
    }
    if (response.statuses) {
      apiData.value.statuses = response.statuses;
    }

    // 设置默认值
    if (response.reasons && response.reasons.length > 0) {
      formData.value.reason = response.reasons[0].id;
    }
  } catch (error) {
    console.error('获取产品信息失败:', error);
    // ElMessage.error('获取产品信息失败')
  } finally {
    loading.value = false;
  }
};

// 获取产品图片
const getProductImage = (product) => {
  return `http://47.237.73.30/${product.image}`;
};

// 触发文件上传
const triggerFileUpload = () => {
  fileInput.value?.click();
};

// 处理文件上传
const handleFileUpload = async (event) => {
  const files = Array.from(event.target.files);

  for (const file of files) {
    if (uploadedImages.value.length >= 5) {
      ElMessage.warning(t('zui_duo_zhi_neng_shang_chuan_5_zhang_tu_pian'));
      break;
    }

    try {
      const formData = new FormData();
      formData.append('file', file);
      formData.append('type', 'rma');

      const response = await UploadFile(formData);
      console.log('处理文件上传', response);
      uploadedImages.value.push({
        url: response.url,
        path: response.path,
      });
    } catch (error) {
      console.error('图片上传失败:', error);
      //   ElMessage.error('图片上传失败');
    }
  }

  // 清空文件输入
  event.target.value = '';
};

// 移除图片
const removeImage = (index) => {
  uploadedImages.value.splice(index, 1);
};

// 提交售后申请
const submitAftersale = async () => {
  if (!formData.value.type) {
    ElMessage.error(t('qing_xuan_ze_fu_wu_lei_xing'));
    return;
  }

  if (!formData.value.reason) {
    ElMessage.error(t('qing_xuan_ze_tui_huo_yuan_yin'));
    return;
  }

  try {
    submitting.value = true;

    const submitData = {
      order_product_id: route.query.order_product_id,
      type: formData.value.type,
      quantity: formData.value.quantity,
      opened: formData.value.opened,
      rma_reason_id: formData.value.reason,
      comment: formData.value.comment,
      images: uploadedImages.value.map((img) => img.url),
    };

    await SubmitRMA(submitData);
    ElMessage.success(t('shou_hou_shen_qing_ti_jiao_cheng_gong'));
    router.push('/aftersale');
  } catch (error) {
    console.error('提交售后申请失败:', error);
    // ElMessage.error('提交售后申请失败')
  } finally {
    submitting.value = false;
  }
};

// 返回上一页
const goBack = () => {
  router.back();
};

onMounted(() => {
  loadProductInfo();
});
</script>

<style lang="scss" scoped>
@import url('./aftersaleApply.scss');
</style>

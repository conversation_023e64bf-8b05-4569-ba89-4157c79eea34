.aftersale_detail_container {
  min-height: 68.4rem;
  background: #ffffff;
  padding: 2rem;
  border-radius: 2rem;

  .header1 {
    display: flex;
    align-items: center;
    margin-bottom: 2.4rem;
    background: #ffffff;
    padding: 2rem;
    border-radius: 1.2rem;

    .back_btn {
      width: 2.4rem;
      height: 2.4rem;
      margin-right: 1.6rem;
      cursor: pointer;

      &:hover {
        opacity: 0.7;
      }
    }

    .title {
      font-family: PingFang SC, PingFang SC;
      font-weight: 600;
      font-size: 2rem;
      color: #242426;
      margin: 0;
    }
  }

  .content {
    background: #ffffff;
    border-radius: 1.2rem;
    padding: 2.4rem;
  }

  .product_info {
    display: flex;
    padding: 2rem;
    border: 0.1rem solid #e7e7e7;
    border-radius: 1.2rem;
    margin-bottom: 2.4rem;

    .product_image {
      width: 10rem;
      height: 10rem;
      margin-right: 2rem;
      flex-shrink: 0;

      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
        border-radius: 0.8rem;
      }
    }

    .product_details {
      flex: 1;

      .product_header {
        display: flex;
        // justify-content: space-between;
        align-items: flex-start;
        margin-bottom: 1.2rem;

        .product_name {
          font-family: PingFang SC, PingFang SC;
          font-weight: 600;
          font-size: 2rem;
          color: #242426;
          margin: 0;
          line-height: 2.4rem;
        }

        .status_badge {
          padding: 0.4rem 1.2rem;
          background: #fff4de;
          color: #e59802;
          border-radius: 1.2rem;
          font-size: 1.2rem;
          font-weight: 500;
          white-space: nowrap;
          margin-left: 2rem;
        }
      }

      .product_quantity {
        font-family: PingFang SC, PingFang SC;
        font-weight: 400;
        font-size: 1.4rem;
        color: #70707b;
        margin-bottom: 1.6rem;
      }

      .detail_grid {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 1rem 2rem;

        .detail_item {
          display: flex;
          align-items: center;

          .label {
            font-family: PingFang SC, PingFang SC;
            font-weight: 400;
            font-size: 1.6rem;
            color: #70707b;
            white-space: nowrap;
            margin-right: 0.8rem;
          }

          .value {
            font-family: PingFang SC, PingFang SC;
            font-weight: 400;
            font-size: 1.6rem;
            color: #242426;
            word-break: break-all;
          }
        }
      }
    }
  }

  .info_section {
    margin-bottom: 2.4rem;

    .section_title {
      display: flex;
      align-items: center;
      margin-bottom: 1.6rem;

      .title_bar {
        width: 0.4rem;
        height: 1.6rem;
        background: #6e4aeb;
        border-radius: 0.2rem;
        margin-right: 1.2rem;
      }

      span {
        font-family: PingFang SC, PingFang SC;
        font-weight: 600;
        font-size: 1.8rem;
        color: #242426;
      }
    }

    .images_container {
      display: flex;
      flex-wrap: wrap;
      gap: 1.2rem;

      .image_item {
        width: 8rem;
        height: 8rem;
        cursor: pointer;

        img {
          width: 100%;
          height: 100%;
          object-fit: cover;
          border-radius: 0.8rem;
          border: 0.1rem solid #e7e7e7;
          transition: all 0.3s ease;

          &:hover {
            transform: scale(1.05);
            box-shadow: 0 0.4rem 1.2rem rgba(0, 0, 0, 0.15);
          }
        }
      }
    }

    .no_content {
      font-family: PingFang SC, PingFang SC;
      font-weight: 400;
      font-size: 1.6rem;
      color: #70707b;
      padding: 2rem;
      background: #f8f9fa;
      border-radius: 0.8rem;
    }

    .comment_content {
      font-family: PingFang SC, PingFang SC;
      font-weight: 400;
      font-size: 1.6rem;
      color: #242426;
      line-height: 2rem;
      padding: 1.6rem;
      background: #f8f9fa;
      border-radius: 0.8rem;
      min-height: 4rem;
    }
  }

  .empty_state {
    text-align: center;
    padding: 4rem;
    background: #ffffff;
    border-radius: 1.2rem;
    color: #999;
    font-size: 1.4rem;

    .retry_btn {
      margin-top: 1rem;
      padding: 0.8rem 1.6rem;
      background: #6e4aeb;
      color: #fff;
      border-radius: 0.4rem;
      cursor: pointer;
      display: inline-block;

      &:hover {
        background: #5a3dc9;
      }
    }
  }

  .preview_container {
    text-align: center;

    .preview_image {
      max-width: 100%;
      max-height: 60vh;
      object-fit: contain;
    }
  }
}

// Element Plus Dialog 样式覆盖
:deep(.el-dialog) {
  border-radius: 1.2rem;
}

:deep(.el-dialog__header) {
  padding: 2rem 2rem 1rem 2rem;
}

:deep(.el-dialog__body) {
  padding: 1rem 2rem 2rem 2rem;
}

<template>
  <div class="address_box">
    <div class="address_box_title">
      {{ t('center.location') }}
      <div class="address_box_title_action" @click="showAddDialog">
        {{ t('center.add') }}
        <img src="@assets/add.png" class="address_box_title_action_icon" />
      </div>
    </div>
    <div v-loading="loading" class="address_list">
      <div class="address_box_item" v-for="item in addressList" :key="item.id">
        <div class="address_box_item_user">
          {{ item.name }}&nbsp;&nbsp;{{ item.phone }}&nbsp;&nbsp;
          <span v-if="item.default" class="default_tag">{{
            t('cart.default')
          }}</span>
        </div>
        <div class="address_box_item_detail">
          {{ formatAddress(item) }}
        </div>
        <div class="address_box_item_action">
          <img
            src="@assets/edit.png"
            class="address_box_item_action_icon"
            @click="editAddress(item)"
          />
          <img
            src="@assets/trash.png"
            class="address_box_item_action_icon"
            @click="deleteAddress(item)"
          />
        </div>
      </div>
      <div v-if="addressList.length === 0 && !loading" class="empty_state">
        <p>{{ t('center.noAddress') }}</p>
      </div>
    </div>

    <!-- 地址弹框组件 -->
    <AddressDialog
      v-model:visible="dialogVisible"
      :address-data="currentAddress"
      @success="handleAddressSuccess"
    />
  </div>
</template>
<script setup>
import { ref, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import { useI18n } from 'vue-i18n';
import { GetAddressList, DeleteAddress } from '@api/address';
import { ElMessage, ElMessageBox } from 'element-plus';
import AddressDialog from '@components/AddressDialog.vue';

const { t, locale } = useI18n();
const router = useRouter();

const addressList = ref([]);
const loading = ref(false);
const dialogVisible = ref(false);
const currentAddress = ref(null);

// 获取地址列表
const loadAddressList = async () => {
  try {
    loading.value = true;
    const response = await GetAddressList();
    console.log('地址列表数据:', response);

    addressList.value = response.addresses || [];
  } catch (error) {
    console.error('获取地址列表失败:', error);
    if (error.message?.includes('401')) {
      ElMessage.error(t('qing_xian_deng_lu'));
      router.push('/login');
    } else {
      // ElMessage.error('获取地址列表失败');
    }
  } finally {
    loading.value = false;
  }
};

// 格式化地址显示
const formatAddress = (address) => {
  const parts = [
    address.country_name || '',
    address.zone_name || '',
    address.city || '',
    address.address_1 || '',
    address.address_2 || '',
  ].filter((part) => part.trim());

  return parts.join(' ');
};

// 显示新增地址弹框
const showAddDialog = () => {
  currentAddress.value = null;
  dialogVisible.value = true;
};

// 编辑地址
const editAddress = (address) => {
  currentAddress.value = { ...address };
  dialogVisible.value = true;
};

// 删除地址
const deleteAddress = async (address) => {
  try {
    await ElMessageBox.confirm(
      t('que_ding_yao_shan_chu_zhe_ge_di_zhi_ma'),
      t('que_ren_shan_chu'),
      {
        confirmButtonText: t('que_ding'),
        cancelButtonText: t('qu_xiao'),
        type: 'warning',
      }
    );

    await DeleteAddress(address.id);
    ElMessage.success(t('di_zhi_yi_shan_chu'));

    // 从本地列表中移除
    const index = addressList.value.findIndex((a) => a.id === address.id);
    if (index > -1) {
      addressList.value.splice(index, 1);
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除地址失败:', error);
      //   ElMessage.error('删除地址失败');
    }
  }
};

// 地址操作成功回调
const handleAddressSuccess = () => {
  dialogVisible.value = false;
  loadAddressList(); // 重新加载地址列表
};

onMounted(() => {
  loadAddressList();
});
</script>
<style lang="scss" scoped>
@import url('./address.scss');

.address_box_title_action {
  cursor: pointer;
}

.address_list {
  min-height: 200px;
}

.default_tag {
  color: #6e4aeb;
  font-weight: 600;
  background: #f3effe;
  padding: 0.2rem 0.8rem;
  border-radius: 0.4rem;
  font-size: 1.2rem;
}

.address_box_item_action_icon {
  cursor: pointer;
  transition: opacity 0.2s;

  &:hover {
    opacity: 0.7;
  }
}

.empty_state {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 3rem;
  color: #999;
  font-size: 1.4rem;

  p {
    margin: 0;
  }
}
</style>

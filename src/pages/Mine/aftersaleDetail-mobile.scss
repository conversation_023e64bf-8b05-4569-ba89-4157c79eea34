/**
 * 售后详情 - 移动端样式
 */
@media screen and (max-width: 768px) {
  .aftersale_detail_container {
    padding: 0;
    background-color: #f5f5f5;

    /* 头部样式 */
    .header1 {
      position: sticky;
      top: 0;
      z-index: 10;
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 1.2rem 1rem;
      background-color: #fff;
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
      border-radius: 0;
      margin-bottom: 0;

      .back_btn {
        position: absolute;
        left: 1.5rem;
        width: 2rem;
        height: 2rem;
        cursor: pointer;
      }

      .title {
        font-size: 1.6rem;
        font-weight: 500;
        margin: 0;
      }
    }

    /* 内容区域 */
    .content {
      padding: 1rem;
      margin-top: 1rem;
      background-color: #f5f5f5;

      /* 产品信息 */
      .product_info {
        background-color: #fff;
        border-radius: 0.8rem;
        padding: 1.5rem;
        margin-bottom: 1.5rem;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
        display: flex;
        flex-direction: column;

        .product_image {
          width: 10rem;
          height: 10rem;
          margin: 0 auto 1.5rem;
          display: flex;
          align-items: center;
          justify-content: center;

          img {
            max-width: 100%;
            max-height: 100%;
            object-fit: contain;
          }
        }

        .product_details {
          flex: 1;

          .product_header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 1rem;

            .product_name {
              font-size: 1.6rem;
              font-weight: bold;
              margin: 0;
              flex: 1;
            }

            .status_badge {
              padding: 0.3rem 0.8rem;
              font-size: 1.2rem;
              background-color: #f0f7ff;
              color: #2d8cf0;
              border-radius: 1rem;
              white-space: nowrap;
            }
          }

          .product_quantity {
            font-size: 1.4rem;
            color: #666;
            margin-bottom: 1.5rem;
          }

          .detail_grid {
            display: flex;
            flex-direction: column;
            gap: 1rem;

            .detail_item {
              display: flex;

              .label {
                color: #666;
                min-width: 5em;
                flex-shrink: 0;
              }

              .value {
                color: #333;
                flex: 1;
              }
            }
          }
        }
      }

      /* 图片和备注信息 */
      .info_section {
        background-color: #fff;
        border-radius: 0.8rem;
        padding: 1.5rem;
        margin-bottom: 1.5rem;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);

        .section_title {
          display: flex;
          align-items: center;
          margin-bottom: 1.5rem;

          .title_bar {
            width: 0.4rem;
            height: 1.6rem;
            background-color: #6e4aeb;
            margin-right: 1rem;
            border-radius: 0.2rem;
          }

          span {
            font-size: 1.6rem;
            font-weight: 500;
            color: #333;
          }
        }

        .images_container {
          display: flex;
          flex-wrap: wrap;
          gap: 1rem;

          .image_item {
            width: calc(50% - 0.5rem);
            aspect-ratio: 1;
            border-radius: 0.4rem;
            overflow: hidden;

            img {
              width: 100%;
              height: 100%;
              object-fit: cover;
            }
          }
        }

        .comment_content,
        .no_content {
          color: #999;
          font-size: 1.4rem;
          line-height: 1.6;
        }

        .no_content {
          color: #999;
          text-align: center;
          padding: 2rem 1.6rem;
          text-align: left;
        }
      }
    }

    /* 空状态 */
    .empty_state {
      padding: 3rem 1.5rem;
      text-align: center;

      p {
        font-size: 1.4rem;
        color: #999;
        margin-bottom: 1.5rem;
      }

      .retry_btn {
        display: inline-block;
        padding: 0.8rem 2rem;
        background-color: #6e4aeb;
        color: #fff;
        border-radius: 2rem;
        font-size: 1.4rem;
      }
    }
  }

  /* 图片预览 */
  .preview_container {
    display: flex;
    justify-content: center;

    .preview_image {
      max-width: 100%;
      max-height: 80vh;
    }
  }

  /* 状态颜色 */
  .status_badge {
    &.pending {
      background-color: #fff3e0;
      color: #ff9800;
    }

    &.processing {
      background-color: #e3f2fd;
      color: #2196f3;
    }

    &.approved {
      background-color: #e0f2f1;
      color: #009688;
    }

    &.rejected {
      background-color: #fbe9e7;
      color: #f44336;
    }

    &.completed {
      background-color: #e8f5e9;
      color: #4caf50;
    }

    &.cancelled {
      background-color: #f5f5f5;
      color: #9e9e9e;
    }
  }
}

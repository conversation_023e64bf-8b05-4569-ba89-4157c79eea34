/**
 * 订单列表 - 移动端样式
 */
@media screen and (max-width: 768px) {
  .box {
    min-height: auto;
    background: #f5f5f5;
    border-radius: 0;
    padding: 0;

    /* 标题栏样式 */
    &_title {
      height: auto;
      margin: 0;
      padding: 1.2rem 1rem;
      background-color: #fff;
      position: sticky;
      top: 0;
      z-index: 10;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 1.6rem;
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
      position: relative;

      &_back {
        position: absolute;
        left: 1.5rem;
        width: 2rem;
        height: 2rem;
        cursor: pointer;
      }
    }

    /* 标签页样式 */
    &_tabs {
      display: flex;
      padding: 0;
      background-color: #fff;
      overflow-x: auto;
      white-space: nowrap;
      -webkit-overflow-scrolling: touch;
      position: sticky;
      top: 4.5rem;
      z-index: 9;
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
      margin-bottom: 1rem;
      justify-content: space-between;
      height: auto;
      padding: 0 2rem;

      &::-webkit-scrollbar {
        display: none;
      }

      &_item {
        padding: 1rem 0;
        font-size: 1.3rem;
        flex: 1;
        text-align: center;
        position: relative;
        color: #666;

        &.active {
          color: #6e4aeb;
          font-weight: 500;

          /* 只保留一个底线 */
          &::after {
            display: none;
          }
        }
      }
    }

    /* 订单列表样式 */
    &_list {
      padding: 0 1rem;

      &_item {
        background-color: #fff;
        border-radius: 0.8rem;
        margin-bottom: 1rem;
        overflow: hidden;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
        position: relative;

        /* 移除订单状态标签，改为内联显示 */

        &_time {
          padding: 0 1rem;
          font-size: 1.2rem;
          color: #666;
          border-bottom: 1px solid #f0f0f0;

          /* 只在移动端下使用flex布局 */
          display: flex;
          justify-content: flex-start;

          .order_date {
            flex-shrink: 0;
          }

          .order_number {
            flex-shrink: 1;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            text-align: right;
          }
        }
        &_main {
          display: block;
          padding: 0;

          &_td {
            width: 100% !important;
            padding: 1rem;
            border-bottom: 1px solid #f5f5f5;
            display: flex;

            &:last-child {
              border-bottom: none;
              padding-top: 0;
              display: flex;
              flex-direction: column;
              align-items: flex-end;

              .order_total {
                display: flex;
                align-items: center;
                margin-bottom: 1.2rem;
                width: 100%;
                justify-content: flex-end;
                padding: 0 1rem 0.5rem;

                .order_total_label {
                  font-size: 1.2rem;
                  color: #666;
                  margin-right: 0.5rem;
                }

                .order_total_value {
                  font-size: 1.6rem;
                  font-weight: bold;
                  color: #333;
                }
              }

              .order_btns {
                display: flex;
                justify-content: flex-end;
                gap: 1rem;
                padding: 0 1rem 1rem;
                width: 100%;
                margin-bottom: 1rem;

                button {
                  padding: 0.5rem 1.2rem;
                  border-radius: 1.5rem;
                  font-size: 1.2rem;
                  background: #fff;
                  cursor: pointer;

                  &.primary {
                    color: #fff;
                    background-color: #6e4aeb;
                    border: none;
                  }

                  &.default {
                    color: #333;
                    background-color: #f5f5f5;
                    border: 1px solid #e0e0e0;
                  }
                }
              }
            }

            &.w383 {
              border-bottom: none;
              padding-bottom: 0.5rem;
              position: relative;
            }

            &_img {
              width: 7rem;
              height: 7rem;
              margin-right: 1.2rem;
              object-fit: contain;
            }

            &_info {
              flex: 1;
              display: flex;
              flex-direction: column;
              justify-content: space-between;

              &_header {
                display: flex;
                justify-content: space-between;
                align-items: flex-start;
                margin-bottom: 0.5rem;
              }

              &_title {
                font-size: 1.4rem;
                color: #333;
                flex: 1;
                display: -webkit-box;
                -webkit-line-clamp: 2;
                line-clamp: 2;
                -webkit-box-orient: vertical;
                overflow: hidden;
                text-overflow: ellipsis;
              }

              &_status {
                font-size: 1.2rem;
                padding: 0.2rem 0.8rem;
                border-radius: 1rem;
                background-color: #fff8e6;
                color: #ff9500;
                margin-left: 0.5rem;
                white-space: nowrap;
                flex-shrink: 0;
              }

              &_desc {
                font-size: 1.2rem;
                color: #999;
                margin-bottom: 0.5rem;
              }

              &_price {
                display: flex;
                align-items: center;
                font-size: 1.2rem;

                span {
                  &.price {
                    font-size: 1.4rem;
                    font-weight: bold;
                    color: #333;
                  }

                  &.qty {
                    color: #999;
                    margin-left: 0.5rem;
                  }
                }
              }
            }
          }
        }
      }
    }
  }

  /* 订单详情样式 */
  .detail {
    flex: 1;

    &_name {
      font-size: 1.4rem;
      font-weight: 500;
      margin-bottom: 0.5rem;
      color: #333;
      line-height: 1.4;
    }

    &_num {
      font-size: 1.2rem;
      color: #666;
    }
  }

  /* 订单状态样式 - 在移动端不显示，因为状态已经通过data-status属性显示 */
  .box_list_item_main_td.w200 {
    display: none;
  }

  /* 订单价格样式 - 在移动端不显示，因为价格已经在商品信息里显示 */
  .box_list_item_main_td.w122 {
    display: none;
  }

  /* 按钮区域样式 - 移动端使用order_btns类，不再使用这些类 */
  .box_list_item_main_td.flex.flex1 {
    padding: 0;

    /* 隐藏PC端的按钮，使用移动端特有的按钮替代 */
    .pay,
    .info {
      display: none;
    }
  }

  /* 空状态样式 */
  .empty_state {
    padding: 8rem 2rem !important;
    text-align: center;
    background-color: #fff;
    border-radius: 0.8rem;
    margin: 1rem;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);

    p {
      color: #999;
      font-size: 1.4rem;
    }
  }

  /* 分页样式 */
  .pagination {
    padding: 1.5rem 1rem;
    display: flex;
    justify-content: center;
    align-items: center;
    background-color: transparent;

    .page_btn {
      padding: 0.6rem 1.2rem;
      font-size: 1.3rem;
      background-color: #fff;
      border: none;
      border-radius: 2rem;
      color: #333;
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);

      &:disabled {
        opacity: 0.5;
        background-color: #f5f5f5;
      }
    }

    .page_info {
      font-size: 1.3rem;
      margin: 0 1.5rem;
      color: #666;
    }
  }

  /* 移动端订单状态样式 - 基础样式 */
  .box_list_item_main_td_info_status {
    font-size: 1.2rem;
    padding: 0.2rem 0.8rem;
    border-radius: 1rem;
    margin-left: 0.5rem;
    white-space: nowrap;
    flex-shrink: 0;
    margin-right: 2.5rem;

    /* 不同状态的样式 */
    &.status-unpaid {
      background-color: #fff8e6;
      color: #ff9500;
    }

    &.status-paid {
      background-color: #e6f7ff;
      color: #1890ff;
    }

    &.status-shipped {
      background-color: #e6fffb;
      color: #13c2c2;
    }

    &.status-completed {
      background-color: #f6ffed;
      color: #52c41a;
    }

    &.status-cancelled {
      background-color: #f5f5f5;
      color: #999;
    }
  }
}

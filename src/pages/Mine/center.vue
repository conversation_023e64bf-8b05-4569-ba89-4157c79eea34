<template>
  <div class="center_box">
    <!-- 移动端用户信息显示 -->
    <div class="mobile_user_info" v-if="deviceStore.isMobile">
      <div class="mobile_user_info_header">
        <img :src="userInfo.avatar" class="mobile_user_info_avatar" />
        <div class="mobile_user_info_details">
          <h2 class="mobile_user_info_name">{{ userInfo.name || 'Guest' }}</h2>
          <p class="mobile_user_info_email">
            {{ userInfo.email || 'Not logged in' }}
          </p>
        </div>
        <!-- 当前路由名称和菜单 -->
        <div class="mobile_user_info_route_name" @click="toggleMobileMenu">
          <span class="mobile_user_info_route_name_text">{{
            t('center.home')
          }}</span>
          <img
            src="@assets/arrow.png"
            alt="菜单"
            class="mobile_user_info_route_name_icon"
            :class="{ active: showMobileMenu }"
          />
        </div>
      </div>

      <!-- 移动端菜单弹窗 -->
      <div class="mobile_menu_dropdown" :class="{ active: showMobileMenu }">
        <div
          class="mobile_menu_dropdown_item"
          v-for="item in menuList"
          :key="item.id"
          @click="onGoUrl(item.url)"
          :class="{ active: route.name === item.url }"
        >
          <img
            :src="route.name == item.url ? item.iconActive : item.icon"
            class="mine_box_menu_item_icon"
          />
          <span>{{ t(item.name) }}</span>
        </div>
        <div
          class="mobile_menu_dropdown_item"
          v-if="isLoggedIn"
          @click="onLogout"
          style="text-align: center; color: red; padding: 1.5rem"
        >
          <span>{{ t('tui_chu_deng_lu') }}</span>
        </div>
      </div>
    </div>

    <!-- 订单状态统计 -->
    <div
      class="center_box_tabs"
      :class="{ 'mobile-tabs': deviceStore.isMobile }"
    >
      <div class="center_box_tabs_item" @click="goToOrderList('waitPay')">
        <img src="@assets/waitPay.png" class="center_box_tabs_item_icon" />
        <div class="center_box_tabs_item_main">
          <div class="center_box_tabs_item_main_num">
            {{ orderStats.waitPay || 0 }}
          </div>
          <div class="center_box_tabs_item_main_title">
            {{ t('center.waitPay') }}
          </div>
        </div>
      </div>
      <div class="center_box_tabs_item" @click="goToOrderList('waitDeliver')">
        <img src="@assets/waitDevely.png" class="center_box_tabs_item_icon" />
        <div class="center_box_tabs_item_main">
          <div class="center_box_tabs_item_main_num">
            {{ orderStats.waitDeliver || 0 }}
          </div>
          <div class="center_box_tabs_item_main_title">
            {{ t('center.waitDeliver') }}
          </div>
        </div>
      </div>
      <div class="center_box_tabs_item" @click="goToOrderList('waitReceive')">
        <img src="@assets/waitPay.png" class="center_box_tabs_item_icon" />
        <div class="center_box_tabs_item_main">
          <div class="center_box_tabs_item_main_num">
            {{ orderStats.waitReceive || 0 }}
          </div>
          <div class="center_box_tabs_item_main_title">
            {{ t('center.waitReceive') }}
          </div>
        </div>
      </div>
      <div class="center_box_tabs_item" @click="goToOrderList('waitAfterSale')">
        <img src="@assets/waitDevely.png" class="center_box_tabs_item_icon" />
        <div class="center_box_tabs_item_main">
          <div class="center_box_tabs_item_main_num">
            {{ orderStats.waitAfterSale || 0 }}
          </div>
          <div class="center_box_tabs_item_main_title">
            {{ t('center.waitAfterSale') }}
          </div>
        </div>
      </div>
    </div>
    <div class="center_box_section">
      <div class="center_box_section_title">
        <span>{{ t('center.title') }}</span>
        <div class="center_box_section_title_all" @click="goToOrderList">
          {{ t('center.all') }}
          <img
            src="@assets/arrowIcon.png"
            class="center_box_section_title_all_icon"
          />
        </div>
      </div>
      <div class="center_box_section_line"></div>
      <div class="center_box_section_list" v-if="recentOrders.length > 0">
        <div
          class="center_box_section_list_item"
          v-for="order in recentOrders"
          :key="order.number"
          @click="viewOrderDetail(order)"
        >
          <div class="order_card">
            <div class="order_card_image">
              <img
                :src="
                  order.showUrl ||
                  'https://p0.ssl.img.360kuai.com/dmfd/158_88_75/t11508c75c8423c35fb6c06b871.webp?size=1024x819'
                "
                class="product_img"
              />
            </div>
            <div class="order_card_content">
              <div class="order_info_line">
                <span class="order_label">{{ t('center.orderNo') }}：</span>
                <span class="order_value">{{
                  deviceStore.isMobile
                    ? t('center.orderNo') + ': ' + order.number
                    : order.number
                }}</span>
                <div class="product_line">|</div>
                <span class="product_count">{{
                  '共 ' + (order.quantity || 1) + ' ' + t('center.desc')
                }}</span>
              </div>
              <div class="order_info_line">
                <span class="order_label">{{ t('center.createTime') }}：</span>
                <span class="order_value">{{
                  deviceStore.isMobile
                    ? formatDateMobile(order.created_at)
                    : formatDate(order.created_at)
                }}</span>
              </div>
            </div>
            <div class="order_card_status">
              <div class="status_badge">
                {{ order.status_format || getOrderStatusText(order.status) }}
              </div>
              <div class="order_price">{{ order.total_format }}</div>
              <div
                class="view_detail"
                @click="viewOrderDetail(order)"
                v-if="!deviceStore.isMobile"
              >
                {{ t('center.operate') }}
              </div>
            </div>
          </div>
        </div>
      </div>
      <div v-else class="empty_orders">
        {{ t('wish.empty') }}
      </div>
    </div>
  </div>
</template>
<script setup>
import { ref, onMounted, reactive, inject, computed } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import { useI18n } from 'vue-i18n';
import { ElMessageBox } from 'element-plus';
import { GetAccountInfo } from '@api/account';
import { GetOrderStats } from '@api/order';
import { useDeviceStore } from '@/stores/device';
import { useUserStore } from '@/stores/user';
import moment from 'moment';
// 导入菜单图标
import HomeActive from '@assets/home_active.png';
import Home from '@assets/<EMAIL>';
import Info from '@assets/info.png';
import InfoActive from '@assets/<EMAIL>';
import Pwd from '@assets/pwd.png';
import PwdActive from '@assets/<EMAIL>';
import Order from '@assets/order.png';
import OrderActive from '@assets/<EMAIL>';
import Loaction from '@assets/location.png';
import LoactionActive from '@assets/<EMAIL>';
import Collect from '@assets/collect.png';
import CollectActive from '@assets/<EMAIL>';
import Message from '@assets/message.png';
import MessageActive from '@assets/<EMAIL>';

const { t, locale } = useI18n();
const router = useRouter();
const route = useRoute();
const deviceStore = useDeviceStore();
const userStore = useUserStore();
const refreshUserInfo = inject('refreshUserInfo', () => {});

// 移动端菜单状态
const showMobileMenu = ref(false);
const logoutLoading = ref(false);

// 判断用户是否已登录
const isLoggedIn = computed(() => {
  return !!userInfo.email; // 如果用户邮箱存在，则认为已登录
});

// 菜单列表
const menuList = ref([
  {
    id: 0,
    name: 'center.home',
    icon: Home,
    iconActive: HomeActive,
    url: 'Center',
  },
  {
    id: 1,
    name: 'center.info',
    icon: Info,
    iconActive: InfoActive,
    url: 'User',
  },
  {
    id: 2,
    name: 'center.pwd',
    icon: Pwd,
    url: 'Pwd',
    iconActive: PwdActive,
  },
  {
    id: 3,
    name: 'center.order',
    icon: Order,
    iconActive: OrderActive,
    url: 'Order',
  },
  {
    id: 4,
    name: 'center.location',
    icon: Loaction,
    iconActive: LoactionActive,
    url: 'Address',
  },
  {
    id: 5,
    name: 'center.collect',
    icon: Collect,
    iconActive: CollectActive,
    url: 'Collect',
  },
  {
    id: 6,
    name: 'center.message',
    icon: Message,
    iconActive: MessageActive,
    url: 'Aftersale',
  },
]);

// 切换移动端菜单显示
const toggleMobileMenu = () => {
  showMobileMenu.value = !showMobileMenu.value;
};

// 退出登录方法
const onLogout = async () => {
  try {
    await ElMessageBox.confirm(
      t('que_ding_yao_tui_chu_deng_lu_ma'),
      t('que_ren_tui_chu'),
      {
        confirmButtonText: t('que_ding'),
        cancelButtonText: t('qu_xiao'),
        type: 'warning',
      }
    );

    logoutLoading.value = true;

    // 使用 Pinia store 的 logout 方法清除状态
    userStore.logout();

    // 清除本地存储的认证信息
    localStorage.removeItem('token');
    localStorage.removeItem('user_info');
    sessionStorage.removeItem('token');
    sessionStorage.removeItem('user_info');

    // 跳转到登录页
    router.push('/login');
  } catch (error) {
    if (error !== 'cancel') {
      console.error('退出登录失败:', error);
    }
  } finally {
    logoutLoading.value = false;
    showMobileMenu.value = false; // 关闭菜单
  }
};

// 用户信息
const userInfo = reactive({
  name: '',
  email: '',
  avatar: '',
});

const orderStats = ref({
  waitPay: 0,
  waitDeliver: 0,
  waitReceive: 0,
  waitAfterSale: 0,
});

const recentOrders = ref([]);
const loading = ref(false);

// 获取订单统计数据和用户信息
const loadOrderStats = async () => {
  try {
    loading.value = true;

    // 使用个人信息接口获取数据
    const accountInfo = await GetAccountInfo();
    console.log('账户信息', accountInfo);

    if (accountInfo) {
      // 设置用户信息
      if (accountInfo.customer) {
        userInfo.name = accountInfo.customer.name || '';
        userInfo.email = accountInfo.customer.email || '';
        userInfo.avatar = accountInfo.customer.avatar || '';
      }

      // 设置最近订单
      recentOrders.value =
        accountInfo.latest_orders.map((order) => ({
          ...order,
          total_format: `$${order.total}`,
          showUrl: `http://47.237.73.30/${order.image}`,
        })) || [];
    }
  } catch (error) {
    console.error('获取账户信息失败:', error);
  } finally {
    loading.value = false;
  }
};

// 格式化日期 - 完整格式
const formatDate = (dateString) => {
  const date = new Date(dateString);
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit',
  });
};

// 格式化日期 - 移动端简化格式
const formatDateMobile = (dateString) => {
  const date = new Date(dateString);
  return (
    date.toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
    }) +
    ' ' +
    date.toLocaleTimeString('zh-CN', {
      hour: '2-digit',
      minute: '2-digit',
    })
  );
};

// 获取订单状态文本
const getOrderStatusText = (status) => {
  const statusMap = {
    pending_payment: t('center.waitPay'),
    processing: t('center.waitDeliver'),
    shipped: t('center.waitReceive'),
    completed: t('yi_wan_cheng'),
    cancelled: t('yi_qu_xiao'),
    refund_pending: t('center.waitAfterSale'),
  };
  return statusMap[status] || status;
};

// 跳转到订单列表，可以指定状态过滤
const goToOrderList = (status) => {
  if (status) {
    router.push({
      path: '/order',
      query: { status },
    });
  } else {
    router.push('/order');
  }
};

// 导航到其他页面
const onGoUrl = (url) => {
  router.push({ name: url });
};

// 查看订单详情
const viewOrderDetail = (order) => {
  router.push(`/orderDetail?order_number=${order.number}`);
};

onMounted(() => {
  loadOrderStats();
  GetOrderStats().then((res) => {
    console.log('订单统计', res);
    orderStats.value = {
      waitPay: res?.unpaid || 0,
      waitDeliver: res?.paid || 0,
      waitReceive: res?.shipped || 0,
      waitAfterSale: res?.completed || 0,
    };
  });
});
</script>
<style lang="scss" scoped>
@import url('./center.scss');
@import url('./center-mobile.scss');

/* 订单卡片样式 */
.order_card {
  display: flex;
  align-items: center;
  background: #fff;
  /* border-radius: 0.8rem; */
  padding: 1.5rem;
  /* margin-bottom: 1rem; */
  /* box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    border: 1px solid #f0f0f0; */
  border-bottom: 1px solid #f0f0f0;
  cursor: pointer;
}

.order_card_image {
  margin-right: 1.5rem;

  .product_img {
    width: 6rem;
    height: 6rem;
    object-fit: cover;
    border-radius: 0.6rem;
    border: 1px solid #eee;
  }
}

.order_card_content {
  flex: 1;

  .order_info_line {
    display: flex;
    align-items: center;
    margin-bottom: 0.8rem;

    &:last-child {
      margin-bottom: 0;
    }
  }

  .order_label {
    font-size: 1.6rem;
    color: #666;
    margin-right: 0.5rem;
  }

  .order_value {
    font-size: 1.6rem;
    color: #333;
    font-weight: 500;
  }

  .product_line {
    margin: 0 0.8rem;
    color: #ccc;
  }

  .product_count {
    font-size: 1.6rem;
    color: #999;
  }
}

.order_card_status {
  display: flex;
  align-items: center;
  /* gap: 8.8rem; */

  .status_badge {
    padding: 0.4rem 0.8rem;
    background: #fff3cd;
    color: #856404;
    border-radius: 1rem;
    font-size: 1.6rem;
    font-weight: 500;
  }

  .order_price {
    font-size: 1.8rem;
    font-weight: 600;
    color: #333;
    margin: 0 10rem;
    min-width: 10rem;
  }

  .view_detail {
    font-size: 1.6rem;
    color: #6e4aeb;
    cursor: pointer;

    &:hover {
      color: #5a3dc9;
    }
  }
}

.empty_orders {
  text-align: center;
  padding: 2rem;
  color: #999;
  font-size: 1.4rem;
}

.center_box_section_title_all {
  cursor: pointer;
  font-size: 1.6rem;
}
</style>

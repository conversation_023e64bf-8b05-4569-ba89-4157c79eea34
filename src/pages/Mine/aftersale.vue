<template>
  <div class="box" :class="{ 'is-mobile': isMobile }" v-loading="loading">
    <h4 class="box_title">
      <img src="@assets/back.png" class="box_title_back" @click="goBack" />
      {{ t('center.message') }}
    </h4>
    <div class="box_thead">
      <div class="box_thead_th w383">{{ t('center.product') }}</div>
      <div class="box_thead_th w149">{{ t('center.num') }}</div>
      <div class="box_thead_th w154">{{ t('center.type') }}</div>
      <div class="box_thead_th w202">{{ t('center.createTime1') }}</div>
      <div class="box_thead_th w92">{{ t('center.operate1') }}</div>
    </div>
    <!-- 桌面端显示 -->
    <div
      class="box_tbody desktop-view"
      v-if="aftersaleList.length > 0 && !isMobile"
    >
      <div
        class="box_tr"
        v-for="item in aftersaleList"
        :key="item.id"
        @click="viewDetail(item)"
      >
        <div class="box_tr_td w383">
          <img
            :src="getProductImage(item)"
            class="box_tr_td_img"
            :alt="item.order_product?.name"
          />
          {{ item.product_name || '' }}
        </div>
        <div class="box_tr_td price w149">x {{ item.quantity || 1 }}</div>
        <div class="box_tr_td price w154">{{ item.type_format }}</div>
        <div class="box_tr_td price w202">{{ item.created_at }}</div>
        <div class="box_tr_td del w92" @click="viewDetail(item)">
          {{ t('cha_kan') }}
        </div>
      </div>
    </div>

    <!-- 移动端显示 -->
    <div
      class="box_tbody mobile-view"
      v-if="aftersaleList.length > 0 && isMobile"
    >
      <div
        class="aftersale-item-mobile"
        v-for="item in aftersaleList"
        :key="item.id"
        @click="viewDetail(item)"
      >
        <!-- 服务类型标签 -->
        <div class="service-type-tag">
          {{ item.type_format || '保修' }}
        </div>

        <div class="item-header">
          <img
            :src="getProductImage(item)"
            class="product-image"
            :alt="item.order_product?.name"
          />
          <div class="product-info">
            <div class="product-name">
              {{ item.product_name || 'Ledger Flex' }}
            </div>
            <div class="product-quantity">x {{ item.quantity || 1 }}</div>
          </div>
        </div>
        <div class="item-details">
          <div class="detail-row">
            <div class="label">{{ t('center.createTime1') }}：</div>
            <div class="value">{{ item.created_at }}</div>
          </div>
        </div>
      </div>
    </div>

    <div v-else-if="!loading" class="empty_state">
      <p>{{ t('zan_wu_shou_hou_ji_lu') }}</p>
    </div>

    <!-- 分页 -->
    <div v-if="pagination.total > pagination.limit" class="pagination">
      <button
        :disabled="pagination.page <= 1"
        @click="changePage(pagination.page - 1)"
        class="page_btn"
      >
        {{ t('shang_yi_ye') }}
      </button>
      <span class="page_info">
        {{ pagination.page }} /
        {{ Math.ceil(pagination.total / pagination.limit) }}
      </span>
      <button
        :disabled="
          pagination.page >= Math.ceil(pagination.total / pagination.limit)
        "
        @click="changePage(pagination.page + 1)"
        class="page_btn"
      >
        {{ t('xia_yi_ye') }}
      </button>
    </div>
  </div>
</template>
<script setup>
import { ref, onMounted, computed } from 'vue';
import { useRouter } from 'vue-router';
import { useI18n } from 'vue-i18n';
import { ElMessage } from 'element-plus';
import { GetRMAList } from '@api/account';
import { useDeviceStore } from '@/stores/device';

const { t } = useI18n();
const router = useRouter();
const deviceStore = useDeviceStore();

const isMobile = computed(() => deviceStore.isMobile);
const loading = ref(false);
const aftersaleList = ref([]);
const pagination = ref({
  page: 1,
  limit: 10,
  total: 0,
});

// 获取售后列表
const loadAftersaleList = async () => {
  try {
    loading.value = true;
    const params = {
      page: pagination.value.page,
      limit: pagination.value.limit,
    };

    const response = await GetRMAList(params);
    console.log('售后列表:', response);

    // 处理返回数据，确保创建日期格式正确
    const rmas = response.rmas || [];
    aftersaleList.value = rmas.map((item) => ({
      ...item,
      created_at: formatDate(item.created_at),
    }));
    pagination.value.total = response.total || 0;
  } catch (error) {
    console.error('获取售后列表失败:', error);
    // ElMessage.error('获取售后列表失败');
  } finally {
    loading.value = false;
  }
};

// 获取产品图片
const getProductImage = (item) => {
  return `http://47.237.73.30/${item.image}`;
};

// 格式化日期
const formatDate = (dateString) => {
  if (!dateString) return '';
  const date = new Date(dateString);
  return isMobile.value
    ? `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(
        2,
        '0'
      )}-${String(date.getDate()).padStart(2, '0')}`
    : date.toLocaleDateString('zh-CN');
};

// 查看详情
const viewDetail = (item) => {
  router.push(`/aftersale/detail?id=${item.id}`);
};

// 切换页码
const changePage = (page) => {
  pagination.value.page = page;
  loadAftersaleList();
};

// 返回上一页
const goBack = () => {
  router.back();
};

onMounted(() => {
  loadAftersaleList();
});
</script>
<style lang="scss" scoped>
@import url('./aftersale.scss');
@import url('./aftersale-mobile.scss');
</style>

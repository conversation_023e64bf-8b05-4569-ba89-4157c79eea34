/* 订单详情移动端样式 */
@media screen and (max-width: 768px) {
  .box {
    padding: 0;
    background-color: #f5f5f5;
    min-height: 100vh;
    overflow-y: visible;
    font-size: 1.4rem;

    &_title {
      padding: 1.2rem 1rem;
      background-color: #fff;
      position: sticky;
      top: 0;
      z-index: 10;
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
      display: flex;
      align-items: center;
      justify-content: center;
      margin: 0 0 1rem 0;
      font-size: 1.6rem;

      &_back {
        position: absolute;
        left: 1.5rem;
        width: 1.8rem;
        height: 1.8rem;
      }

      /* 移动端底部显示操作按钮 */
      &_action {
        display: none; /* 在顶部隐藏，移到底部显示 */
      }
    }

    /* 商品卡片样式 */
    .product_card {
      margin: 0 1rem 1rem;
      background-color: #fff;
      border-radius: 0.8rem;
      overflow: hidden;
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
      padding: 1.2rem;
      display: flex;

      &_image {
        width: 7rem;
        height: 7rem;
        margin-right: 1rem;
        flex-shrink: 0;

        img {
          width: 100%;
          height: 100%;
          object-fit: contain;
          border-radius: 0.4rem;
        }
      }

      &_content {
        flex: 1;

        .product_card_header {
          display: flex;
          align-items: flex-start;
          justify-content: space-between;
          margin-bottom: 0.8rem;
        }

        .product_name {
          font-size: 1.5rem;
          margin: 0;
          flex: 1;
          font-weight: 500;
        }

        .product_status {
          font-size: 1.2rem;
          color: #ff9500;
          background-color: #fff8e6;
          padding: 0.3rem 0.8rem;
          border-radius: 1rem;
          white-space: nowrap;
        }

        .product_quantity {
          color: #999;
          margin-bottom: 0.5rem;
          font-size: 1.3rem;
        }

        .product_price {
          font-size: 1.6rem;
          font-weight: 600;
          color: #333;
          margin-bottom: 1rem;
        }
      }
    }

    /* 订单详情行样式 */
    .order_details {
      border-top: 1px solid #f0f0f0;
      padding-top: 1rem;
      grid-template-columns: auto !important;

      .order_detail_row {
        margin-bottom: 0.6rem;
        font-size: 1.2rem;

        .label {
          color: #999;
        }

        .value {
          color: #333;
        }
      }
    }

    /* 地址信息区域样式 */
    &_subtitle {
      margin: 1.5rem 1rem 0.5rem;
      padding: 0;
      font-size: 1.4rem;
      font-weight: 500;
      position: relative;
      display: flex;
      align-items: center;
      color: #333;

      label {
        width: 0.3rem;
        height: 1.4rem;
        background-color: #6e4aeb;
        margin-right: 0.8rem;
        border-radius: 0.15rem;
      }
    }

    &_address {
      background-color: #fff;
      border-radius: 0.8rem 0.8rem 0 0;
      margin: 0 1rem;
      padding: 1rem;
      display: flex;
      font-size: 1.3rem;
      color: #666;
      border-bottom: 1px solid #f0f0f0;

      &_item {
        flex: 1;
        padding: 0 0.5rem;
        font-weight: 500;
      }
    }

    &_addressInfo {
      background-color: #fff;
      border-radius: 0 0 0.8rem 0.8rem;
      margin: 0 1rem;
      padding: 1rem;
      display: flex;
      font-size: 1.2rem;
      margin-bottom: 1.2rem;

      &_item {
        flex: 1;
        padding: 0 0.5rem;

        &_desc {
          margin: 0.4rem 0;
          line-height: 1.5;
          color: #666;
        }
      }
    }

    /* 订单状态区域样式 */
    &_goods {
      background-color: #fff;
      border-radius: 0 0 0.8rem 0.8rem;
      padding: 1rem;
      margin: 0 1rem 1.2rem;

      &_info {
        display: flex;
        font-size: 1.2rem;
        padding: 0.5rem 0;
        border-bottom: 1px solid #f5f5f5;

        &_name {
          flex: 1;
          color: #333;
        }

        &_num {
          flex: 1;
          color: #666;
        }

        &_price {
          color: #999;
          text-align: right;
        }
      }
    }

    /* 订单备注区域样式 */
    &_note {
      background-color: #f5f5f5;
      border-radius: 0.8rem;
      padding: 1.2rem;
      margin: 0 1rem 1.2rem;
      color: #666;
      font-size: 1.2rem;
    }

    /* 订单金额详情样式 */
    &_detail {
      background-color: #fff;
      border-radius: 0.8rem 0.8rem 0 0;
      padding: 1.2rem;
      font-size: 1.2rem;
      color: #666;
      margin: 0 1rem;

      p {
        margin: 0.4rem 0;
      }
    }

    &_line {
      height: 1px;
      background-color: #f0f0f0;
      margin: 0 1rem;
    }

    &_total {
      background-color: #fff;
      border-radius: 0 0 0.8rem 0.8rem;
      padding: 1.2rem;
      font-size: 1.3rem;
      color: #333;
      text-align: right;
      margin: 0 1rem 8rem; /* 为底部按钮留出空间 */

      span {
        font-size: 1.6rem;
        color: #ff4d4f;
        font-weight: 600;
        margin-left: 0.5rem;
      }
    }

    /* 底部操作按钮样式 */
    &_action {
      position: fixed;
      bottom: 0;
      left: 0;
      right: 0;
      padding: 1rem 1.5rem;
      background-color: #fff;
      display: flex;
      box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.05);

      &_item {
        flex: 1;
        text-align: center;
        padding: 0.8rem 0;
        border-radius: 3rem;
        font-size: 1.4rem;
        margin: 0 0.5rem;
        font-weight: 500;
        display: flex;
        align-items: center;
        justify-content: center;

        &.pay {
          background-color: #6e4aeb;
          color: #fff;
        }

        &.cancel {
          background-color: #f5f5f5;
          color: #333;
          border: 1px solid #e0e0e0;
        }
      }
    }

    /* 空状态样式 */
    .empty_state {
      margin: 0 1rem;
      padding: 2rem;
      text-align: center;
      background-color: #fff;
      border-radius: 0.8rem;

      p {
        color: #999;
        margin-bottom: 1rem;
      }

      .retry_btn {
        display: inline-block;
        padding: 0.8rem 2rem;
        background-color: #6e4aeb;
        color: #fff;
        border-radius: 2rem;
        font-size: 1.3rem;
      }
    }
  }
}

/* 确保垂直方向有足够的间距 */
.is-mobile .box {
  padding-bottom: 8rem;
}

.center_box {
  // 移动端用户信息样式
  .mobile_user_info {
    background-color: #ffffff;
    border-radius: 1.2rem;
    padding: 1.5rem;
    margin-bottom: 1.5rem;

    &_header {
      display: flex;
      align-items: center;
    }

    &_avatar {
      width: 6rem;
      height: 6rem;
      border-radius: 50%;
      object-fit: cover;
    }

    &_details {
      flex: 1;
      padding-left: 1.5rem;
    }

    &_name {
      margin: 0;
      font-size: 1.8rem;
      font-weight: 600;
      color: #333;
      margin-bottom: 0.5rem;
    }

    &_email {
      margin: 0;
      font-size: 1.4rem;
      color: #666;
    }

    &_arrow {
      img {
        width: 2rem;
        height: 2rem;
      }
    }
  }

  &_tabs {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 1.1rem;
    margin-bottom: 1.2rem;

    // 移动端状态栏样式
    &.mobile-tabs {
      display: grid;
      grid-template-columns: repeat(2, 1fr);
      gap: 0.8rem;
      padding: 0 1rem;

      .center_box_tabs_item {
        width: auto;
        flex: none;
        padding: 1.2rem;
        border-radius: 0.8rem;
        background-color: #fff;
        box-shadow: none;
        border: none;

        &_icon {
          width: 2.6rem;
          height: 2.6rem;
        }

        &_main {
          padding-left: 1rem;

          &_num {
            height: auto;
            font-size: 1.8rem;
            line-height: 1.2;
            margin-bottom: 0.2rem;
            font-weight: 700;
          }

          &_title {
            height: auto;
            font-size: 1.2rem;
            line-height: 1.2;
            color: #999;
          }
        }
      }
    }

    &_item {
      display: flex;
      flex: 1;
      align-items: center;
      padding: 2.8rem 0 2.8rem 3rem;
      box-sizing: border-box;
      background: #ffffff;
      border-radius: 2rem;
      cursor: pointer;
      transition: all 0.2s ease;

      &:hover {
        background: #f8f8f8;
      }

      &_icon {
        width: 4.8rem;
        height: 4.8rem;
      }
      &_main {
        flex: 1;
        padding-left: 3rem;
        box-sizing: border-box;
        &_num {
          height: 2.4rem;
          font-family: PingFang SC, PingFang SC;
          font-weight: 600;
          font-size: 2.4rem;
          color: #242426;
          line-height: 2.4rem;
          margin-bottom: 0.5rem;
        }
        &_title {
          height: 2.4rem;
          font-family: PingFang SC, PingFang SC;
          font-weight: 400;
          font-size: 1.6rem;
          color: #70707b;
          line-height: 2.4rem;
        }
      }
    }
  }
  &_section {
    height: auto;
    min-height: 30rem;
    max-height: 57.4rem;
    background: #ffffff;
    border-radius: 2rem;
    box-sizing: border-box;
    padding: 3rem 2.4rem;

    // 移动端适配
    @media screen and (max-width: 768px) {
      padding: 1.5rem 1rem;
      border-radius: 0;
      margin-bottom: 2rem;
      height: auto;
      min-height: auto;
      max-height: none;
    }
    &_title {
      display: flex;
      align-items: center;
      justify-content: space-between;
      height: 2.8rem;
      font-family: PingFang SC, PingFang SC;
      font-weight: 400;
      font-size: 2rem;
      color: #242426;
      margin-bottom: 3rem;

      @media screen and (max-width: 768px) {
        font-size: 1.6rem;
        height: 2.4rem;
        margin-bottom: 1.5rem;
      }

      &_all {
        display: flex;
        align-items: center;
        font-family: PingFang SC, PingFang SC;
        font-weight: 400;
        font-size: 1.4rem;
        color: #70707b;

        @media screen and (max-width: 768px) {
          font-size: 1.2rem !important;
        }

        &_icon {
          margin-left: 0.4rem;
          width: 1.4rem;
          height: 1.4rem;
        }
      }
    }
    &_line {
      width: 100%;
      height: 0;
      border: 0.1rem solid #e7e7e7;
    }
    & &_list {
      height: auto;
      max-height: 45.6rem;
      overflow-y: auto;

      @media screen and (max-width: 768px) {
        max-height: unset;
        height: auto;
        overflow-y: visible;
      }
    }
  }
}

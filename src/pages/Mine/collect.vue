<template>
  <div class="collect_box">
    <h4 class="collect_box_title">{{ t('center.collect') }}</h4>
    <div class="collect_box_thead">
      <div class="collect_box_thead_th w700">{{ t('center.product') }}</div>
      <div class="collect_box_thead_th w280">{{ t('center.price') }}</div>
    </div>
    <div class="collect_box_tbody" v-loading="loading">
      <div
        class="collect_box_tr"
        v-for="item in wishlist"
        :key="item.id"
        @click="goProductDetail(item.product_id)"
      >
        <div class="collect_box_tr_td w700">
          <img
            :src="
              item.image ||
              'https://p0.ssl.img.360kuai.com/dmfd/158_88_75/t11508c75c8423c35fb6c06b871.webp?size=1024x819'
            "
            class="collect_box_tr_td_img"
          />
          <div class="product_info">
            <div class="product_name">{{ item.product_name }}</div>
            <!-- <div class="product_description">{{ item.product.description }}</div> -->
          </div>
        </div>
        <div class="collect_box_tr_td price w280">
          {{ item.price }}
        </div>
      </div>
      <div v-if="wishlist.length === 0 && !loading" class="empty_state">
        <p>{{ t('center.noCollect') }}</p>
      </div>
    </div>
  </div>
</template>
<script setup>
import { ref, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import { useI18n } from 'vue-i18n';
import { GetWishlist, RemoveFromWishlist } from '@api/wishlist';
import { ElMessage, ElMessageBox } from 'element-plus';

const { t, locale } = useI18n();
const router = useRouter();

const wishlist = ref([]);
const loading = ref(false);

const goProductDetail = (id) => {
  router.push(`/goods/${id}`);
};

// 获取收藏列表
const loadWishlist = async () => {
  try {
    loading.value = true;
    const response = await GetWishlist();
    console.log('收藏列表数据:', response);

    wishlist.value = response.wishlist || [];
  } catch (error) {
    console.error('获取收藏列表失败:', error);
    if (error.message?.includes('401')) {
      ElMessage.error(t('qing_xian_deng_lu'));
      router.push('/login');
    } else {
      // ElMessage.error('获取收藏列表失败');
    }
  } finally {
    loading.value = false;
  }
};

// 从收藏列表中移除商品
const removeFromWishlist = async (item) => {
  try {
    await ElMessageBox.confirm(
      t('que_ding_yao_qu_xiao_shou_cang_zhe_ge_shang_pin_ma'),
      t('que_ren_qu_xiao_shou_cang'),
      {
        confirmButtonText: t('que_ding'),
        cancelButtonText: t('qu_xiao'),
        type: 'warning',
      }
    );

    await RemoveFromWishlist(item.id);
    ElMessage.success(t('yi_qu_xiao_shou_cang'));

    // 从本地列表中移除
    const index = wishlist.value.findIndex((w) => w.id === item.id);
    if (index > -1) {
      wishlist.value.splice(index, 1);
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('取消收藏失败:', error);
      // ElMessage.error('取消收藏失败');
    }
  }
};

// 跳转到商品详情页
const goToProduct = (product) => {
  router.push(`/goods/${product.id}`);
};

onMounted(() => {
  loadWishlist();
});
</script>
<style lang="scss" scoped>
@import url('./collect.scss');

.product_info {
  margin-left: 1rem;

  .product_name {
    font-size: 1.8rem;
    font-weight: 600;
    color: #333;
    margin-bottom: 0.5rem;
  }

  .product_description {
    font-size: 1.2rem;
    color: #666;
    line-height: 1.4;
    max-height: 3rem;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
  }
}

.collect_actions {
  display: flex;
  gap: 0.5rem;
  margin-top: 1rem;

  .remove_btn,
  .view_btn {
    padding: 0.5rem 1rem;
    border: 1px solid #ddd;
    background: #fff;
    border-radius: 0.4rem;
    cursor: pointer;
    font-size: 1.2rem;
    transition: all 0.2s;

    &:hover {
      background: #f5f5f5;
    }
  }

  .remove_btn {
    color: #e74c3c;
    border-color: #e74c3c;

    &:hover {
      background: #e74c3c;
      color: #fff;
    }
  }

  .view_btn {
    color: #6e4aeb;
    border-color: #6e4aeb;

    &:hover {
      background: #6e4aeb;
      color: #fff;
    }
  }
}

.empty_state {
  text-align: center;
  padding: 3rem;
  color: #999;
  font-size: 1.4rem;
}
</style>

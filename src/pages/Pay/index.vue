<template>
  <div class="pay_box">
    <div class="pay_box_main">
      <!-- 订单信息 -->
      <div class="order-info">
        <h2 class="order-title">
          {{ t('ding_dan_ti_jiao_cheng_gong_qing_fu_kuan') }}
        </h2>
        <div class="order-details">
          <div class="order-item" v-if="order_number">
            <span class="label">{{ t('center.orderNo') }}：</span>
            <span class="value">{{ order_number }}</span>
          </div>
          <div class="order-item">
            <span class="label">{{ t('zhi_fu_jin_e') }}：</span>
            <span class="value amount">{{ orderInfo.total_format }}</span>
          </div>
          <div class="order-item">
            <span class="label">{{ t('center.paymentType') }}：</span>
            <span class="value">{{ orderInfo.payment_method_name }}</span>
          </div>
        </div>
      </div>

      <!-- Stripe 支付表单 -->
      <div
        v-if="
          stripeLoaded &&
          stripePublishableKey &&
          (orderInfo.payment_method_name?.toLowerCase().includes('stripe') ||
            !orderInfo.payment_method_name?.toLowerCase().includes('paypal'))
        "
        class="payment-form-container"
      >
        <div class="stripe-payment-section">
          <h3 class="payment-title">
            {{ orderInfo.payment_method_name || 'Credit Card' }}
          </h3>

          <!-- Stripe Card Element 容器 -->
          <div class="card-element-container">
            <div id="card-element" class="card-element">
              <!-- Stripe Elements 会在这里插入表单 -->
            </div>
            <div id="card-errors" class="card-errors" v-show="cardError">
              {{ cardError }}
            </div>
          </div>

          <div class="payment-actions">
            <button
              id="submit-button"
              class="stripe-pay-button pay-button"
              :disabled="!cardComplete || processing"
              @click="handlePayment"
            >
              <i class="el-icon-credit-card"></i>
              <span v-if="processing">{{ t('chu_li_zhong_0') }}</span>
              <span v-else
                >{{ t('li_ji_zhi_fu') }} {{ orderInfo.total_format }}</span
              >
            </button>
          </div>
        </div>
      </div>

      <!-- PayPal 支付按钮 -->
      <div
        v-if="orderInfo.payment_method_name?.toLowerCase().includes('paypal')"
        class="payment-form-container"
      >
        <div class="paypal-payment-section">
          <h3 class="payment-title">
            {{ orderInfo.payment_method_name }}
          </h3>

          <!-- PayPal 按钮容器 -->
          <div
            class="paypal-button-container"
            :class="{ processing: processing }"
          >
            <!-- 处理中遮罩 -->
            <div v-if="processing" class="processing-overlay">
              <div class="processing-spinner"></div>
              <p>{{ t('chu_li_zhong_0') }}</p>
            </div>

            <!-- PayPal 按钮将在这里渲染 -->
            <div id="paypal-button-container"></div>
          </div>

          <div v-if="cardError" class="card-errors">
            {{ cardError }}
          </div>
        </div>
      </div>

      <!-- 加载状态 -->
      <div v-if="loading" class="loading-container">
        <div class="loading-spinner"></div>
        <p>{{ t('zheng_zai_jia_zai_zhi_fu_xin_xi') }}</p>
      </div>

      <!-- 无支付配置 -->
      <div
        v-if="
          !loading &&
          !stripePublishableKey &&
          orderInfo.payment_method_name?.toLowerCase().includes('stripe')
        "
        class="error-container"
      >
        <p>{{ t('zhi_fu_pei_zhi_cuo_wu_qing_lian_xi_ke_fu') }}</p>
      </div>

      <!-- 无PayPal支付配置 -->
      <div
        v-if="
          !loading &&
          !paypalClientId &&
          orderInfo.payment_method_name?.toLowerCase().includes('paypal')
        "
        class="error-container"
      >
        <p>{{ t('zhi_fu_pei_zhi_cuo_wu_qing_lian_xi_ke_fu') }}</p>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, nextTick } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { ElMessage } from 'element-plus';
import { loadStripe } from '@stripe/stripe-js';
import { GetOrderPayInfo } from '@/api/order';
import { CaptureStripePayment, CapturePayPalPayment } from '@/api/payment';
import { useI18n } from 'vue-i18n';
const { t, locale } = useI18n();
const route = useRoute();
const router = useRouter();

// 响应式数据
const loading = ref(true);
const processing = ref(false);
const stripeLoaded = ref(false);
const paypalLoaded = ref(false);
const orderInfo = ref({});
const stripePublishableKey = ref('');
const paypalClientId = ref('');
const cardComplete = ref(false);
const cardError = ref('');
const order_number = ref('');

// Stripe 相关变量
let stripe = null;
let elements = null;
let cardElement = null;

const orderNumber = route.query.order_number;

// 获取订单支付信息
const fetchOrderPayInfo = async () => {
  if (!orderNumber) {
    ElMessage.error(t('ding_dan_hao_bu_neng_wei_kong'));
    return;
  }

  try {
    loading.value = true;
    const response = await GetOrderPayInfo(orderNumber);
    console.log('GetOrderPayInfo response:', response);

    if (response.order) {
      orderInfo.value = response.order;
    }

    if (response.payment_setting) {
      // 根据订单中的支付方式名称来判断
      const paymentMethodName = orderInfo.value.payment_method_name || '';

      // 处理Stripe支付
      if (paymentMethodName.toLowerCase().includes('stripe')) {
        // 从payment_setting获取Stripe的publishable_key
        const publishableKey =
          response.payment_setting.publishable_key ||
          response.payment_setting.stripe_publishable_key ||
          response.payment_setting.pk;

        if (!publishableKey) {
          console.error(
            '未找到Stripe publishable key:',
            response.payment_setting
          );
          ElMessage.error(t('zhi_fu_pei_zhi_cuo_wu_qing_lian_xi_ke_fu'));
          return;
        }

        stripePublishableKey.value = publishableKey;
        await initStripe();
      }
      // 处理PayPal支付
      else if (paymentMethodName.toLowerCase().includes('paypal')) {
        // 从payment_setting获取PayPal的client_id
        const clientId =
          response.payment_setting.live_client_id ||
          response.payment_setting.paypal_client_id ||
          response.payment_setting.live_client_id;

        if (!clientId) {
          console.error('未找到PayPal client ID:', response.payment_setting);
          ElMessage.error(t('zhi_fu_pei_zhi_cuo_wu_qing_lian_xi_ke_fu'));
          return;
        }

        paypalClientId.value = clientId;
        await initPayPal();
      } else {
        // 尝试默认使用Stripe支付（原始逻辑）
        const publishableKey =
          response.payment_setting.publishable_key ||
          response.payment_setting.stripe_publishable_key ||
          response.payment_setting.pk;

        if (publishableKey) {
          console.log('使用默认Stripe支付方式:', publishableKey);
          stripePublishableKey.value = publishableKey;
          await initStripe();
        } else {
          console.error(
            '不支持的支付方式:',
            orderInfo.value.payment_method_name
          );
          ElMessage.error(t('bu_zhi_chi_de_zhi_fu_fang_shi'));
        }
      }
    }
  } catch (error) {
    console.error('获取订单支付信息失败:', error);
    // ElMessage.error('获取订单信息失败，请重试');
  } finally {
    loading.value = false;
  }
};

// 初始化PayPal
const initPayPal = async () => {
  try {
    console.log('Initializing PayPal with clientId:', paypalClientId.value);

    // 先设置状态为已加载
    paypalLoaded.value = true;

    // 等待DOM更新，确保PayPal按钮容器已经渲染
    await nextTick();

    // 初始化PayPal SDK
    if (!window.paypal) {
      const script = document.createElement('script');
      script.src = `https://www.paypal.com/sdk/js?client-id=${
        paypalClientId.value
      }&currency=${orderInfo.value.currency || 'USD'}`;

      // 使用Promise等待SDK加载完成
      await new Promise((resolve, reject) => {
        script.onload = () => resolve();
        script.onerror = () => reject(new Error('PayPal SDK加载失败'));
        document.head.appendChild(script);
      });
    }

    if (!window.paypal) {
      throw new Error('PayPal SDK加载失败');
    }

    // 确认PayPal按钮容器存在
    const container = document.getElementById('paypal-button-container');
    if (!container) {
      throw new Error('PayPal按钮容器不存在');
    }

    // 创建PayPal按钮
    window.paypal
      .Buttons({
        // 创建订单
        createOrder: (data, actions) => {
          // 调用后端创建订单API
          console.log('创建PayPal订单:', orderNumber, orderInfo.value.total);

          return actions.order.create({
            purchase_units: [
              {
                amount: {
                  value: parseFloat(orderInfo.value.total),
                  currency_code: orderInfo.value.currency || 'USD',
                },
                custom_id: orderNumber,
              },
            ],
          });
        },
        // 订单批准后的回调
        onApprove: async (data, actions) => {
          try {
            console.log('PayPal订单已批准:', data);

            // 显示处理中状态
            processing.value = true;

            // 捕获订单付款
            const order = await actions.order.capture();
            console.log('PayPal订单已捕获:', order);

            // 调用后端API确认支付
            const result = await CapturePayPalPayment({
              orderNumber,
              paypalOrderId: data.orderID,
            });

            console.log('后端确认支付结果:', result);

            // 显示成功消息
            ElMessage.success(t('zhi_fu_cheng_gong'));

            // 跳转到订单页面
            router.push('/order');
          } catch (error) {
            console.error('PayPal支付处理失败:', error);
            cardError.value =
              error.message || t('zhi_fu_shi_bai_qing_zhong_shi');
            ElMessage.error(t('zhi_fu_shi_bai_qing_zhong_shi'));
          } finally {
            processing.value = false;
          }
        },
        // 错误处理
        onError: (err) => {
          console.error('PayPal错误:', err);
          // cardError.value = err.message || t('zhi_fu_guo_cheng_zhong_fa_sheng_cuo_wu');
          ElMessage.error(t('zhi_fu_guo_cheng_zhong_fa_sheng_cuo_wu'));
        },
      })
      .render('#paypal-button-container');

    console.log('PayPal按钮已渲染');
  } catch (error) {
    console.error('PayPal初始化失败:', error);
    cardError.value =
      error.message ||
      t('jia_zai_zhi_fu_zu_jian_shi_bai_qing_shua_xin_ye_mian_zhong_shi');
    ElMessage.error(
      t('jia_zai_zhi_fu_zu_jian_shi_bai_qing_shua_xin_ye_mian_zhong_shi')
    );
  }
};

// 初始化Stripe
const initStripe = async () => {
  try {
    console.log('Initializing Stripe with key:', stripePublishableKey.value);

    // 加载Stripe实例
    stripe = await loadStripe(stripePublishableKey.value);

    if (!stripe) {
      throw new Error('Failed to load Stripe');
    }

    console.log('Stripe loaded successfully');
    stripeLoaded.value = true;

    // 等待DOM更新后创建Elements
    await nextTick();
    setupStripeElements();
  } catch (error) {
    console.error('Error loading Stripe:', error);
    ElMessage.error(
      t('jia_zai_zhi_fu_zu_jian_shi_bai_qing_shua_xin_ye_mian_zhong_shi')
    );
  }
};

// 设置Stripe Elements
const setupStripeElements = () => {
  try {
    // 创建elements实例
    elements = stripe.elements();

    // 创建card元素
    cardElement = elements.create('card', {
      style: {
        base: {
          fontSize: '16px',
          color: '#424770',
          '::placeholder': {
            color: '#aab7c4',
          },
          fontFamily: '"Helvetica Neue", Helvetica, sans-serif',
          fontSmoothing: 'antialiased',
        },
        invalid: {
          color: '#9e2146',
        },
      },
      hidePostalCode: false,
    });

    // 挂载到DOM
    cardElement.mount('#card-element');

    // 监听变化事件
    cardElement.on('change', (event) => {
      console.log('Card changed:', event);
      cardComplete.value = event.complete;

      if (event.error) {
        cardError.value = event.error.message;
      } else {
        cardError.value = '';
      }
    });

    cardElement.on('ready', () => {
      console.log('Card element ready');
    });

    console.log('Stripe Elements setup complete');
  } catch (error) {
    console.error('Error setting up Stripe Elements:', error);
    ElMessage.error(t('zhi_fu_biao_dan_chu_shi_hua_shi_bai'));
  }
};

// 处理支付
const handlePayment = async () => {
  if (!cardComplete.value || processing.value || !stripe || !cardElement) {
    return;
  }

  try {
    processing.value = true;
    cardError.value = '';

    console.log('Creating token...');

    // 创建Token
    const { token, error } = await stripe.createToken(cardElement);

    if (error) {
      cardError.value = error.message;
      ElMessage.error(error.message);
      return;
    }

    console.log('Stripe token created:', token);

    // 调用后端API处理支付
    const paymentData = {
      order_number: orderNumber,
      token: token.id,
      payment_method: 'stripe',
    };

    const result = await CaptureStripePayment(paymentData);
    console.log('Payment result:', result);

    ElMessage.success(t('zhi_fu_cheng_gong'));

    // 跳转到支付成功页面
    router.push({
      path: '/order',
      //   query: {
      //     order_number: orderNumber,
      //     payment_method: 'stripe',
      //   },
    });
  } catch (error) {
    console.error('支付处理失败:', error);
    const errorMessage = error.message || t('zhi_fu_shi_bai_qing_zhong_shi');
    cardError.value = error.response.data.message || errorMessage;
    ElMessage.error(errorMessage);
  } finally {
    processing.value = false;
  }
};

// 组件挂载时获取订单信息
onMounted(() => {
  fetchOrderPayInfo();
  order_number.value = route.query.order_number;
});
</script>

<style lang="scss" scoped>
@import url('./index.scss');

// Stripe 支付表单的额外样式
.payment-form-container {
  .stripe-payment-section {
    .payment-title {
      font-size: 2rem;
      font-weight: 600;
      color: #242426;
      margin-bottom: 2rem;
      text-align: center;
    }

    .card-element-container {
      margin: 2rem 0;

      .card-element {
        background-color: white;
        padding: 1.6rem 1.4rem;
        border-radius: 0.8rem;
        border: 0.1rem solid #d6d6d6;
        transition: border-color 0.3s ease;

        &:focus-within {
          border-color: #409eff;
          box-shadow: 0 0 0 0.2rem rgba(64, 158, 255, 0.2);
        }
      }

      .card-errors {
        color: #e74c3c;
        font-size: 1.4rem;
        margin-top: 0.8rem;
        min-height: 2rem;
        text-align: left;
      }
    }

    .stripe-pay-button {
      color: #fff;
      cursor: pointer;
      .el-icon {
        margin-right: 0.8rem;
        font-size: 2rem;
      }
    }
  }

  // PayPal 支付表单样式
  .paypal-payment-section {
    .payment-title {
      font-size: 2rem;
      font-weight: 600;
      color: #242426;
      margin-bottom: 2rem;
      text-align: center;
    }

    .paypal-button-container {
      margin: 2rem 0;
      min-height: 10rem;
      position: relative;

      #paypal-button-container {
        width: 100%;
        max-width: 50rem;
        margin: 0 auto;
      }

      &.processing {
        opacity: 0.7;
        pointer-events: none;
      }

      .processing-overlay {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(255, 255, 255, 0.8);
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        z-index: 10;

        .processing-spinner {
          width: 4rem;
          height: 4rem;
          border: 0.4rem solid #f3f3f3;
          border-top: 0.4rem solid #0070ba; /* PayPal蓝色 */
          border-radius: 50%;
          animation: spin 1s linear infinite;
          margin-bottom: 1.5rem;
        }

        p {
          color: #0070ba;
          font-size: 1.6rem;
          margin: 0;
        }
      }
    }

    .card-errors {
      color: #e74c3c;
      font-size: 1.4rem;
      margin-top: 0.8rem;
      min-height: 2rem;
      text-align: center;
    }
  }
}

// 加载和错误状态样式
.loading-container {
  padding: 6rem 3rem;
  text-align: center;

  .loading-spinner {
    width: 4rem;
    height: 4rem;
    border: 0.4rem solid #f3f3f3;
    border-top: 0.4rem solid #409eff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 2rem;
  }

  p {
    color: #666;
    font-size: 1.6rem;
    margin: 0;
  }
}

.error-container {
  padding: 6rem 3rem;
  text-align: center;

  p {
    color: #e74c3c;
    font-size: 1.6rem;
    margin: 0;
  }
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}
</style>

.pay_box {
  min-height: calc(100vh - 250px);
  padding: 3rem 5%;
  box-sizing: border-box;
  background: #f8f9fa;

  &_main {
    max-width: 120rem;
    margin: 0 auto;
    background: #ffffff;
    border-radius: 1.2rem;
    box-shadow: 0 0.4rem 1.2rem rgba(0, 0, 0, 0.1);
    overflow: hidden;

    .order-info {
      padding: 3rem;
      border-bottom: 0.1rem solid #e7e7e7;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: #ffffff;

      .order-title {
        font-size: 2.4rem;
        font-weight: 600;
        margin-bottom: 2rem;
        text-align: center;
      }

      .order-details {
        display: flex;
        justify-content: center;
        gap: 4rem;

        .order-item {
          display: flex;
          align-items: center;
          font-size: 1.6rem;

          .label {
            font-weight: 400;
            opacity: 0.9;
          }

          .value {
            font-weight: 600;
            margin-left: 0.8rem;

            &.amount {
              font-size: 2rem;
              color: #ffd700;
            }
          }
        }
      }
    }

    .payment-methods {
      padding: 3rem;
      border-bottom: 0.1rem solid #e7e7e7;

      .section-title {
        font-size: 2rem;
        font-weight: 600;
        color: #242426;
        margin-bottom: 2rem;
        text-align: center;
      }

      .payment-options {
        display: flex;
        justify-content: center;
        gap: 2rem;
        flex-wrap: wrap;

        .payment-option {
          margin: 0;

          :deep(.el-radio__input) {
            display: none;
          }

          :deep(.el-radio__label) {
            padding: 0;
          }

          .payment-option-content {
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 2rem;
            border: 0.2rem solid #e7e7e7;
            border-radius: 1.2rem;
            cursor: pointer;
            transition: all 0.3s ease;
            min-width: 12rem;
            background: #ffffff;

            &:hover {
              border-color: #409eff;
              box-shadow: 0 0.4rem 0.8rem rgba(64, 158, 255, 0.2);
            }

            .payment-logo {
              height: 4rem;
              width: auto;
              margin-bottom: 1rem;
            }

            span {
              font-size: 1.4rem;
              font-weight: 500;
              color: #242426;
            }
          }

          &.is-checked {
            .payment-option-content {
              border-color: #409eff;
              background: #f0f8ff;
              box-shadow: 0 0.4rem 0.8rem rgba(64, 158, 255, 0.3);

              span {
                color: #409eff;
              }
            }
          }
        }
      }
    }

    .payment-form-container {
      padding: 3rem;

      .stripe-payment-section {
        text-align: center;

        .payment-info {
          background: #f8f9fa;
          border-radius: 0.8rem;
          padding: 2rem;
          margin-bottom: 2rem;

          .info-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1rem;

            &:last-child {
              margin-bottom: 0;
            }

            .label {
              font-size: 1.4rem;
              color: #606266;
            }

            .value {
              font-size: 1.4rem;
              font-weight: 500;
              color: #242426;
            }
          }
        }

        .stripe-pay-button {
          width: 100%;
          max-width: 40rem;
          height: 5rem;
          font-size: 1.8rem;
          font-weight: 600;
          border-radius: 0.8rem;
          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
          border: none;

          &:hover {
            background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
          }

          .el-icon {
            margin-right: 0.8rem;
            font-size: 2rem;
          }
        }
      }

      .wechat-payment {
        text-align: center;

        .qr-code-container {
          display: flex;
          justify-content: center;
          margin-bottom: 2rem;

          #wechat-qr-code {
            width: 20rem;
            height: 20rem;
            border: 0.1rem solid #e7e7e7;
            border-radius: 0.8rem;
            display: flex;
            align-items: center;
            justify-content: center;
            background: #f8f9fa;
          }
        }

        .wechat-tip {
          font-size: 1.6rem;
          color: #606266;
          margin: 0;
        }
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .pay_box {
    padding: 2rem 3%;

    &_main {
      .order-info {
        padding: 2rem;

        .order-title {
          font-size: 2rem;
        }

        .order-details {
          flex-direction: column;
          gap: 1rem;
          align-items: center;

          .order-item {
            font-size: 1.4rem;
          }
        }
      }

      .payment-methods {
        padding: 2rem;

        .payment-options {
          flex-direction: column;
          align-items: center;

          .payment-option {
            width: 100%;
            max-width: 30rem;

            .payment-option-content {
              width: 100%;
            }
          }
        }
      }

      .payment-form-container {
        padding: 2rem;
      }
    }
  }
}

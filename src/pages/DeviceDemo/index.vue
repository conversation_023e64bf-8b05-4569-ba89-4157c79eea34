<template>
  <div class="device-demo">
    <div class="demo-container">
      <h1>设备检测演示</h1>

      <div class="device-info-card">
        <h3>当前设备信息</h3>
        <div class="info-grid">
          <div class="info-item">
            <label>设备类型:</label>
            <span class="device-type" :class="deviceStore.deviceType">
              {{ deviceStore.deviceType }}
            </span>
          </div>

          <div class="info-item">
            <label>屏幕宽度:</label>
            <span>{{ deviceStore.screenWidth }}px</span>
          </div>

          <div class="info-item">
            <label>屏幕高度:</label>
            <span>{{ deviceStore.screenHeight }}px</span>
          </div>

          <div class="info-item">
            <label>设备方向:</label>
            <span>{{ deviceStore.orientation }}</span>
          </div>

          <div class="info-item">
            <label>断点:</label>
            <span>{{ deviceStore.breakpoint }}</span>
          </div>
        </div>
      </div>

      <div class="device-status-card">
        <h3>设备状态</h3>
        <div class="status-grid">
          <div class="status-item" :class="{ active: deviceStore.isMobile }">
            <div class="status-icon">📱</div>
            <div class="status-label">移动端</div>
            <div class="status-value">{{ deviceStore.isMobile ? '是' : '否' }}</div>
          </div>

          <div class="status-item" :class="{ active: deviceStore.isTablet }">
            <div class="status-icon">💻</div>
            <div class="status-label">平板端</div>
            <div class="status-value">{{ deviceStore.isTablet ? '是' : '否' }}</div>
          </div>

          <div class="status-item" :class="{ active: deviceStore.isDesktop }">
            <div class="status-icon">🖥️</div>
            <div class="status-label">桌面端</div>
            <div class="status-value">{{ deviceStore.isDesktop ? '是' : '否' }}</div>
          </div>
        </div>
      </div>

      <div class="responsive-demo">
        <h3>响应式演示</h3>
        <div class="demo-box">
          <div class="mobile-only" v-if="deviceStore.isMobile">
            📱 这个内容只在移动端显示
          </div>

          <div class="tablet-only" v-if="deviceStore.isTablet">
            💻 这个内容只在平板端显示
          </div>

          <div class="desktop-only" v-if="deviceStore.isDesktop">
            🖥️ 这个内容只在桌面端显示
          </div>

          <div class="small-screen" v-if="deviceStore.isSmallScreen">
            📏 小屏幕显示 (≤768px)
          </div>

          <div class="medium-screen" v-if="deviceStore.isMediumScreen">
            📐 中等屏幕显示 (768px - 1024px)
          </div>

          <div class="large-screen" v-if="deviceStore.isLargeScreen">
            📺 大屏幕显示 (>1024px)
          </div>
        </div>
      </div>

      <div class="debug-actions">
        <h3>调试功能</h3>
        <div class="button-group">
          <button @click="forceSetDevice('mobile')" class="demo-btn mobile">
            强制设为移动端
          </button>
          <button @click="forceSetDevice('tablet')" class="demo-btn tablet">
            强制设为平板端
          </button>
          <button @click="forceSetDevice('desktop')" class="demo-btn desktop">
            强制设为桌面端
          </button>
          <button @click="resetDevice" class="demo-btn reset">
            重置自动检测
          </button>
        </div>
      </div>

      <div class="device-info-raw">
        <h3>原始设备信息</h3>
        <pre>{{ JSON.stringify(deviceStore.getDeviceInfo(), null, 2) }}</pre>
      </div>
    </div>
  </div>
</template>

<script setup>
import { useDeviceStore } from '@/stores/device'

const deviceStore = useDeviceStore()

// 强制设置设备类型
const forceSetDevice = (type) => {
  deviceStore.setDeviceType(type)
}

// 重置为自动检测
const resetDevice = () => {
  deviceStore.updateDeviceInfo()
}
</script>

<style scoped>
.device-demo {
  padding: 2rem;
  max-width: 1200px;
  margin: 0 auto;
  font-family: 'Helvetica Neue', Arial, sans-serif;
}

.demo-container h1 {
  text-align: center;
  color: #2c3e50;
  margin-bottom: 3rem;
  font-size: 2.5rem;
}

.device-info-card,
.device-status-card,
.responsive-demo,
.debug-actions,
.device-info-raw {
  background: white;
  border-radius: 12px;
  padding: 2rem;
  margin-bottom: 2rem;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  border: 1px solid #e1e8ed;
}

.device-info-card h3,
.device-status-card h3,
.responsive-demo h3,
.debug-actions h3,
.device-info-raw h3 {
  margin-top: 0;
  margin-bottom: 1.5rem;
  color: #2c3e50;
  font-size: 1.5rem;
}

.info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1rem;
}

.info-item {
  display: flex;
  justify-content: space-between;
  padding: 1rem;
  background: #f8f9fa;
  border-radius: 8px;
  border-left: 4px solid #007bff;
}

.info-item label {
  font-weight: 600;
  color: #495057;
}

.device-type {
  font-weight: bold;
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  text-transform: uppercase;
  font-size: 0.875rem;
}

.device-type.mobile {
  background: #e3f2fd;
  color: #1976d2;
}

.device-type.tablet {
  background: #f3e5f5;
  color: #7b1fa2;
}

.device-type.desktop {
  background: #e8f5e8;
  color: #388e3c;
}

.status-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 1rem;
}

.status-item {
  text-align: center;
  padding: 1.5rem 1rem;
  border-radius: 8px;
  background: #f8f9fa;
  border: 2px solid transparent;
  transition: all 0.3s ease;
}

.status-item.active {
  background: #e8f5e8;
  border-color: #28a745;
  transform: translateY(-2px);
}

.status-icon {
  font-size: 2rem;
  margin-bottom: 0.5rem;
}

.status-label {
  font-weight: 600;
  color: #495057;
  margin-bottom: 0.25rem;
}

.status-value {
  font-size: 0.875rem;
  color: #6c757d;
}

.status-item.active .status-value {
  color: #28a745;
  font-weight: 600;
}

.demo-box {
  padding: 2rem;
  border: 2px dashed #dee2e6;
  border-radius: 8px;
  min-height: 100px;
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.mobile-only,
.tablet-only,
.desktop-only,
.small-screen,
.medium-screen,
.large-screen {
  padding: 1rem;
  border-radius: 6px;
  font-weight: 600;
  text-align: center;
}

.mobile-only {
  background: #e3f2fd;
  color: #1976d2;
}

.tablet-only {
  background: #f3e5f5;
  color: #7b1fa2;
}

.desktop-only {
  background: #e8f5e8;
  color: #388e3c;
}

.small-screen {
  background: #fff3e0;
  color: #f57c00;
}

.medium-screen {
  background: #fce4ec;
  color: #c2185b;
}

.large-screen {
  background: #e0f2f1;
  color: #00695c;
}

.button-group {
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
}

.demo-btn {
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 6px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 0.875rem;
}

.demo-btn.mobile {
  background: #2196f3;
  color: white;
}

.demo-btn.tablet {
  background: #9c27b0;
  color: white;
}

.demo-btn.desktop {
  background: #4caf50;
  color: white;
}

.demo-btn.reset {
  background: #ff9800;
  color: white;
}

.demo-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.device-info-raw pre {
  background: #f8f9fa;
  padding: 1rem;
  border-radius: 6px;
  border: 1px solid #e9ecef;
  overflow-x: auto;
  font-size: 0.875rem;
  line-height: 1.4;
}

/* 移动端适配 */
@media (max-width: 768px) {
  .device-demo {
    padding: 1rem;
  }

  .demo-container h1 {
    font-size: 2rem;
  }

  .device-info-card,
  .device-status-card,
  .responsive-demo,
  .debug-actions,
  .device-info-raw {
    padding: 1.5rem;
  }

  .info-grid {
    grid-template-columns: 1fr;
  }

  .status-grid {
    grid-template-columns: 1fr;
  }

  .button-group {
    flex-direction: column;
  }

  .demo-btn {
    width: 100%;
  }
}
</style>
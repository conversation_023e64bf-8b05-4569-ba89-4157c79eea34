/* 移动端适配样式 */
@media screen and (max-width: 768px) {
  .download_box {
    .banner-box {
      height: auto !important;
      min-height: 50rem;
      padding: 3rem 1.5rem !important;
      gap: 3rem !important;

      &_text {
        margin-bottom: 0 !important;

        &_title {
          font-size: 2.6rem !important;
          line-height: 1.3 !important;
          margin-bottom: 1rem;
        }

        &_desc {
          font-size: 1.6rem !important;
          line-height: 1.5 !important;
        }
      }
    }

    &_img {
      width: 100% !important;
      height: auto !important;
      max-height: 35rem;
      object-fit: contain !important;
    }

    &_info {
      padding: 3rem 1.5rem !important;

      &_name {
        font-size: 2.4rem !important;
        margin-bottom: 1.5rem !important;
      }

      &_desc {
        font-size: 1.6rem !important;
        line-height: 1.5 !important;
      }

      &_tip {
        font-size: 1.4rem !important;
        margin-bottom: 3rem !important;
      }

      &_list {
        flex-direction: column;
        gap: 2rem !important;

        &_item {
          width: 90% !important;
          max-width: 25rem !important;
          height: 8rem !important;

          &_main {
            &_logo {
              width: 4rem !important;
              height: 4rem !important;
              margin-right: 1rem !important;
            }

            &_label {
              &_title {
                font-size: 1.2rem !important;
              }

              &_desc {
                font-size: 1.8rem !important;
              }
            }
          }
        }
      }
    }
  }
}

<template>
  <div
    class="product_list"
    :class="{
      'single-column': singleColumn,
      mobile: deviceStore.isMobile,
      tablet: deviceStore.isTablet,
      desktop: deviceStore.isDesktop,
    }"
  >
    <div
      v-for="item in products"
      :key="item.id"
      class="product_list_item"
      :class="{ 'single-column-item': singleColumn }"
      @click="onShowDetail(item.id)"
    >
      <img
        :src="item.images && item.images.length > 0 ? item.images[0] : ''"
        class="product_list_item_img"
        :class="{ 'single-column-img': singleColumn }"
        :alt="item.name"
      />
      <h2 class="product_list_item_name">
        {{ item.name }}
      </h2>
      <div class="product_list_item_info">
        <div class="product_list_item_info_price">
          {{ item.price_format }}
        </div>
        <div class="product_list_item_info_sale">
          {{ t('home.sale') }}: {{ item.sales || 0 }}
        </div>
      </div>
    </div>
  </div>
</template>
<script setup>
import { defineProps } from 'vue';
import { useI18n } from 'vue-i18n';
import { useRouter } from 'vue-router';
import { useDeviceStore } from '@/stores/device';
const deviceStore = useDeviceStore();

const { t } = useI18n();
const router = useRouter();

// 定义props接收商品数据
const props = defineProps({
  products: {
    type: Array,
    default: () => [],
  },
  singleColumn: {
    type: Boolean,
    default: false,
  },
});

const onShowDetail = (id) => {
  router.push(`/goods/${id}`);
};
</script>
<style lang="scss" scoped>
.product_list {
  display: flex;
  gap: 2.6rem;
  flex-wrap: wrap;
  justify-content: space-between;

  &.single-column {
    flex-direction: column;
    align-items: center;
    gap: 1.6rem;
  }

  &_item {
    width: calc(25% - 1.95rem); /* 默认每行4个，减去间距 */
    /* height: 43.6rem; */
    background: #ffffff;
    border-radius: 1.9rem;
    padding: 2.7rem 2.9rem;
    box-sizing: border-box;
    cursor: pointer;

    &.single-column-item {
      width: 100%;
      max-width: 100%;
      height: auto;
      display: flex;
      flex-direction: column;
      padding: 3rem;
    }

    &_img {
      width: 100%;
      height: auto;
      aspect-ratio: 1/1;
      margin-bottom: 3.1rem;
      object-fit: cover;
      border-radius: 1rem;

      &.single-column-img {
        width: 30rem;
        height: 30rem;
        margin: 0 auto 2rem;
      }
    }

    &_name {
      line-height: 3.3rem;
      font-family: PingFang SC, PingFang SC;
      font-weight: 600;
      font-size: 2.3rem;
      color: #242426;
      margin-bottom: 2.9rem;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }

    &_info {
      display: flex;
      align-items: center;
      justify-content: space-between;

      &_price {
        padding: 0 0.8rem;
        height: 35px;
        background: #f3effe;
        border-radius: 0.4rem;
        display: flex;
        align-items: center;
        justify-content: center;
        font-family: PingFang SC, PingFang SC;
        font-weight: 600;
        font-size: 1.9rem;
        color: #6e4aeb;
      }

      &_sale {
        font-family: PingFang SC, PingFang SC;
        font-weight: 400;
        font-size: 1.6rem;
        color: #70707b;
      }
    }
  }
}

/* 移动设备上的响应式布局 */
.product_list.mobile {
  .product_list_item {
    width: 100%; /* 移动端单列 */
  }
}

/* 在非移动设备上，无论屏幕宽度如何，保持每行4个商品 */
.product_list.desktop,
.product_list.tablet {
  .product_list_item {
    width: calc(
      25% - 1.95rem
    ) !important; /* 强制覆盖其他样式，确保大于768px时始终保持每行4个 */
  }
}
</style>

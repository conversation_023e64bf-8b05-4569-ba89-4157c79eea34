import { getToken, getUserInfo } from './storage';

/**
 * 检查用户是否已登录
 * @returns {boolean} 是否已登录
 */
export const isAuthenticated = () => {
  const token = getToken();
  return !!token;
};

/**
 * 检查token是否过期（简单检查，实际应该解析JWT token）
 * @returns {boolean} token是否有效
 */
export const isTokenValid = () => {
  const token = getToken();
  if (!token) return false;

  // 这里可以添加JWT token解析逻辑
  // 目前只是简单检查token是否存在
  try {
    // 如果是JWT token，可以解析并检查过期时间
    // const payload = JSON.parse(atob(token.split('.')[1]))
    // const currentTime = Date.now() / 1000
    // return payload.exp > currentTime

    return true;
  } catch (error) {
    console.error('Token验证失败:', error);
    return false;
  }
};

/**
 * 获取用户角色
 * @returns {string|null} 用户角色
 */
export const getUserRole = () => {
  const userInfo = getUserInfo();
  return userInfo?.role || null;
};

/**
 * 检查用户是否有指定权限
 * @param {string} permission 权限名称
 * @returns {boolean} 是否有权限
 */
export const hasPermission = (permission) => {
  const userInfo = getUserInfo();
  const permissions = userInfo?.permissions || [];
  return permissions.includes(permission);
};

/**
 * 检查用户是否是管理员
 * @returns {boolean} 是否是管理员
 */
export const isAdmin = () => {
  const role = getUserRole();
  return role === 'admin' || role === 'super_admin';
};

/**
 * 格式化用户显示名称
 * @returns {string} 用户显示名称
 */
export const getUserDisplayName = () => {
  const userInfo = getUserInfo();
  if (!userInfo) return '未登录';

  return userInfo.name || userInfo.username || userInfo.email || '用户';
};

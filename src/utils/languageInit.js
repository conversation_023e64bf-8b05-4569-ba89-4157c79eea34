import { getLanguage, setLanguage, isFirstVisit } from './storage';

// 重新导出 isFirstVisit 函数以便其他模块使用
export { isFirstVisit };

/**
 * 支持的语言列表
 */
export const SUPPORTED_LANGUAGES = [
  { id: 0, name: 'Español', lang: 'es' },
  { id: 1, name: 'Français', lang: 'fr' },
  { id: 2, name: 'Indonesia', lang: 'id' },
  { id: 3, name: 'Italiano', lang: 'it' },
  { id: 4, name: '日本語', lang: 'ja' },
  { id: 5, name: '한국어', lang: 'ko' },
  { id: 6, name: 'Русский', lang: 'Py' },
  { id: 7, name: '繁體中文', lang: 'hk' },
  { id: 8, name: 'Deutsch', lang: 'de' },
  { id: 9, name: '简体中文', lang: 'zh' },
  { id: 10, name: 'English', lang: 'en' },
];

/**
 * 获取浏览器语言设置并转换成我们支持的语言代码
 * @returns {string} 语言代码
 */
export const getBrowserLanguage = () => {
  // 获取浏览器语言，例如 'zh-CN', 'en-US', 'fr', 'de' 等
  const browserLang = navigator.language || navigator.userLanguage;

  // 转换为小写并提取主要语言代码（如果有地区代码）
  let mainLang = browserLang.toLowerCase().split('-')[0];

  // 处理中文的特殊情况
  if (mainLang === 'zh') {
    // 区分简体中文和繁体中文
    if (
      browserLang.toLowerCase().includes('cn') ||
      browserLang.toLowerCase().includes('hans')
    ) {
      return 'zh'; // 简体中文
    } else if (
      browserLang.toLowerCase().includes('tw') ||
      browserLang.toLowerCase().includes('hk') ||
      browserLang.toLowerCase().includes('hant')
    ) {
      return 'hk'; // 繁体中文
    }
    // 默认简体中文
    return 'zh';
  }

  // 检查是否为支持的语言
  const isSupported = SUPPORTED_LANGUAGES.some(
    (item) => item.lang === mainLang
  );
  if (isSupported) {
    return mainLang;
  }

  // 默认返回英文
  return 'en';
};

/**
 * 初始化语言设置
 * 优先级：
 * 1. 本地存储的语言设置（如果有且有效）
 * 2. 浏览器语言设置（如果匹配支持的语言）
 * 3. 默认英文
 * @returns {string} 最终确定的语言代码
 */
export const initializeLanguage = () => {
  const savedLanguage = getLanguage();

  if (savedLanguage) {
    // 如果有保存的语言设置，验证是否有效
    const isValidLang = SUPPORTED_LANGUAGES.some(
      (item) => item.lang === savedLanguage
    );

    if (isValidLang) {
      console.log('使用保存的语言设置:', savedLanguage);
      return savedLanguage;
    } else {
      console.warn('保存的语言设置无效:', savedLanguage, '将使用浏览器语言');
    }
  }

  // 如果没有保存的语言设置或设置无效，使用浏览器语言
  const browserLang = getBrowserLanguage();
  console.log('检测到浏览器语言:', navigator.language, '映射为:', browserLang);

  // 保存到本地存储
  setLanguage(browserLang);

  return browserLang;
};

/**
 * 检查是否为有效的语言代码
 * @param {string} lang 语言代码
 * @returns {boolean} 是否有效
 */
export const isValidLanguage = (lang) => {
  return SUPPORTED_LANGUAGES.some((item) => item.lang === lang);
};

/**
 * 根据语言代码获取语言名称
 * @param {string} lang 语言代码
 * @returns {string} 语言名称
 */
export const getLanguageName = (lang) => {
  const langItem = SUPPORTED_LANGUAGES.find((item) => item.lang === lang);
  return langItem ? langItem.name : 'English';
};

/**
 * 转换语言代码为API格式
 * @param {string} lang 前端语言代码
 * @returns {string} API语言代码
 */
export const convertToApiLanguage = (lang) => {
  if (lang === 'zh') {
    return 'zh_cn';
  } else if (lang === 'hk') {
    return 'zh_hk';
  }
  return lang;
};

/**
 * 转换API语言代码为前端格式
 * @param {string} apiLang API语言代码
 * @returns {string} 前端语言代码
 */
export const convertFromApiLanguage = (apiLang) => {
  if (apiLang === 'zh_cn') {
    return 'zh';
  } else if (apiLang === 'zh_hk') {
    return 'hk';
  }
  return apiLang;
};

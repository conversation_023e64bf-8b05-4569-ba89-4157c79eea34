import { ref } from 'vue';

// 简单的事件总线实现
const listeners = {};

// 通过ref创建响应式数据
export const cartUpdated = ref(false);
export const currencyUpdated = ref(false);
export const languageUpdated = ref(false);

// 触发购物车更新
export function triggerCartUpdate() {
  cartUpdated.value = !cartUpdated.value;
}

// 触发币种更新
export function triggerCurrencyUpdate() {
  currencyUpdated.value = !currencyUpdated.value;
}

// 触发语言更新
export function triggerLanguageUpdate() {
  languageUpdated.value = !languageUpdated.value;
}

// 事件监听和触发方法
export default {
  on(event, callback) {
    if (!listeners[event]) {
      listeners[event] = [];
    }
    listeners[event].push(callback);
  },

  emit(event, ...args) {
    if (listeners[event]) {
      listeners[event].forEach((callback) => callback(...args));
    }
  },

  off(event, callback) {
    if (listeners[event]) {
      if (callback) {
        listeners[event] = listeners[event].filter((cb) => cb !== callback);
      } else {
        delete listeners[event];
      }
    }
  },
};

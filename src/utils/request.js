import axios from 'axios';
import { useUserStore } from '@/stores/user';
import { ElMessage } from 'element-plus';
import { getToken, getLanguage, getCurrency } from '@/utils/storage';

// 创建axios实例

const service = axios.create({
  baseURL: 'http://47.237.73.30', // 环境变量配置
  timeout: 10000, // 请求超时时间（ms）
});

// 请求拦截器
service.interceptors.request.use(
  (config) => {
    // 1. 添加token（优先从localStorage获取，确保刷新页面后仍能获取到token）
    const token = getToken();
    // console.log('token:', token);
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }

    // 2. 添加用户设置的语言和币种
    let language = getLanguage();
    const currency = getCurrency();
    if (language == 'zh') {
      language = 'zh_cn';
    } else if (language == 'hk') {
      language = 'zh_hk';
    }

    config.headers['locale'] = language;
    config.headers['currency'] = currency;

    // 3. 统一设置请求头（如Content-Type）
    // 如果是FormData，让浏览器自动设置Content-Type
    if (!(config.data instanceof FormData)) {
      config.headers['Content-Type'] = 'application/json;charset=UTF-8';
    }

    // 3. 可选：显示请求loading（需配合UI库）
    // ElLoading.service({ fullscreen: true });

    return config;
  },
  (error) => {
    console.error('请求拦截错误:', error);
    return Promise.reject(error);
  }
);

// 响应拦截器
service.interceptors.response.use(
  (response) => {
    console.log('响应拦截:', response);
    // 1. 直接返回响应数据（假设后端返回 { code, data, message, status }）
    const { code, data, message, status } = response.data;

    if (status === 'success') {
      return data; // 成功直接返回data
    } else {
      // 2. 处理业务错误（如401未登录）
      if (code === 401 || status === 'false') {
        ElMessage.error('登录过期，请重新登录');

        // 获取userStore实例并清除用户状态
        const userStore = useUserStore();
        userStore.logout();

        // 跳转登录页
        window.location.href = '/login';
      } else {
        ElMessage.error(message || '请求失败');
        return Promise.reject(new Error(message || '请求失败'));
      }
    }
  },
  (error) => {
    // 3. 处理网络错误（如404、500）
    if (error.response) {
      const { status, data } = error.response;

      // 处理401未授权
      if (status === 401) {
        ElMessage.error('登录过期，请重新登录');
        const userStore = useUserStore();
        userStore.logout();
        window.location.href = '/login';
        return Promise.reject(error);
      }

      // 处理其他状态码
      switch (status) {
        case 404:
          ElMessage.error('接口不存在');
          break;
        case 500:
          ElMessage.error('服务器错误');
          break;
        case 403:
          ElMessage.error('没有权限访问');
          break;
        default:
          ElMessage.error(data?.message || `请求失败 (${status})`);
      }
    } else if (error.request) {
      ElMessage.error('网络连接异常，请检查网络');
    } else {
      ElMessage.error('请求配置错误');
    }
    return Promise.reject(error);
  }
);

// 封装GET/POST请求
export function get(url, params = {}) {
  return service.get(url, { params });
}

export function post(url, data = {}) {
  return service.post(url, data);
}

export function put(url, data = {}) {
  return service.put(url, data);
}

export function del(url, params = {}) {
  return service.delete(url, { params });
}

export default service;

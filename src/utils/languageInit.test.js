/**
 * 语言初始化功能测试
 * 这个文件用于测试语言初始化的各种场景
 */

import {
  getBrowserLanguage,
  initializeLanguage,
  isValidLanguage,
  getLanguageName,
  convertToApiLanguage,
  convertFromApiLanguage,
  SUPPORTED_LANGUAGES,
} from './languageInit';

// 模拟不同的浏览器语言设置
const testBrowserLanguages = [
  { input: 'zh-CN', expected: 'zh', description: '简体中文' },
  { input: 'zh-TW', expected: 'hk', description: '繁体中文(台湾)' },
  { input: 'zh-HK', expected: 'hk', description: '繁体中文(香港)' },
  { input: 'en-US', expected: 'en', description: '英文(美国)' },
  { input: 'en-GB', expected: 'en', description: '英文(英国)' },
  { input: 'fr-FR', expected: 'fr', description: '法文' },
  { input: 'de-DE', expected: 'de', description: '德文' },
  { input: 'ja-JP', expected: 'ja', description: '日文' },
  { input: 'ko-KR', expected: 'ko', description: '韩文' },
  { input: 'es-ES', expected: 'es', description: '西班牙文' },
  { input: 'it-IT', expected: 'it', description: '意大利文' },
  { input: 'id-ID', expected: 'id', description: '印尼文' },
  { input: 'ru-RU', expected: 'Py', description: '俄文' },
  { input: 'pt-BR', expected: 'en', description: '葡萄牙文(不支持，回退到英文)' },
  { input: 'ar-SA', expected: 'en', description: '阿拉伯文(不支持，回退到英文)' },
];

// 测试浏览器语言检测
export const testBrowserLanguageDetection = () => {
  console.log('=== 测试浏览器语言检测 ===');
  
  testBrowserLanguages.forEach(({ input, expected, description }) => {
    // 模拟 navigator.language
    const originalNavigator = global.navigator;
    global.navigator = { language: input };
    
    const result = getBrowserLanguage();
    const isCorrect = result === expected;
    
    console.log(
      `${isCorrect ? '✅' : '❌'} ${description}: ${input} -> ${result} (期望: ${expected})`
    );
    
    // 恢复原始 navigator
    global.navigator = originalNavigator;
  });
};

// 测试语言代码转换
export const testLanguageConversion = () => {
  console.log('\n=== 测试语言代码转换 ===');
  
  const testCases = [
    { frontend: 'zh', api: 'zh_cn', description: '简体中文' },
    { frontend: 'hk', api: 'zh_hk', description: '繁体中文' },
    { frontend: 'en', api: 'en', description: '英文' },
    { frontend: 'fr', api: 'fr', description: '法文' },
    { frontend: 'de', api: 'de', description: '德文' },
  ];
  
  testCases.forEach(({ frontend, api, description }) => {
    const toApi = convertToApiLanguage(frontend);
    const fromApi = convertFromApiLanguage(api);
    
    const toApiCorrect = toApi === api;
    const fromApiCorrect = fromApi === frontend;
    
    console.log(
      `${toApiCorrect ? '✅' : '❌'} ${description} 前端->API: ${frontend} -> ${toApi} (期望: ${api})`
    );
    console.log(
      `${fromApiCorrect ? '✅' : '❌'} ${description} API->前端: ${api} -> ${fromApi} (期望: ${frontend})`
    );
  });
};

// 测试语言验证
export const testLanguageValidation = () => {
  console.log('\n=== 测试语言验证 ===');
  
  const validLanguages = ['en', 'zh', 'hk', 'fr', 'de', 'es', 'it', 'ja', 'ko', 'id', 'Py'];
  const invalidLanguages = ['xx', 'invalid', '', null, undefined];
  
  validLanguages.forEach(lang => {
    const isValid = isValidLanguage(lang);
    console.log(`${isValid ? '✅' : '❌'} 有效语言: ${lang}`);
  });
  
  invalidLanguages.forEach(lang => {
    const isValid = isValidLanguage(lang);
    console.log(`${!isValid ? '✅' : '❌'} 无效语言: ${lang}`);
  });
};

// 测试语言名称获取
export const testLanguageNames = () => {
  console.log('\n=== 测试语言名称获取 ===');
  
  SUPPORTED_LANGUAGES.forEach(({ lang, name }) => {
    const retrievedName = getLanguageName(lang);
    const isCorrect = retrievedName === name;
    console.log(`${isCorrect ? '✅' : '❌'} ${lang}: ${retrievedName} (期望: ${name})`);
  });
  
  // 测试无效语言代码
  const invalidName = getLanguageName('invalid');
  console.log(`${invalidName === 'English' ? '✅' : '❌'} 无效语言代码回退: ${invalidName} (期望: English)`);
};

// 运行所有测试
export const runAllTests = () => {
  console.log('🚀 开始语言初始化功能测试...\n');
  
  testBrowserLanguageDetection();
  testLanguageConversion();
  testLanguageValidation();
  testLanguageNames();
  
  console.log('\n✨ 测试完成！');
};

// 如果直接运行此文件，执行所有测试
if (typeof window !== 'undefined') {
  // 在浏览器环境中，将测试函数挂载到 window 对象上
  window.languageTests = {
    runAllTests,
    testBrowserLanguageDetection,
    testLanguageConversion,
    testLanguageValidation,
    testLanguageNames,
  };
  
  console.log('语言测试函数已挂载到 window.languageTests');
  console.log('可以在控制台中运行: window.languageTests.runAllTests()');
}

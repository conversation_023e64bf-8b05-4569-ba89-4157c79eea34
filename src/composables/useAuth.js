import { ref, computed } from 'vue';
import { useRouter } from 'vue-router';
import { ElMessage } from 'element-plus';
import { useUserStore } from '@/stores/user';
import { Login, Register, Logout } from '@/api/auth';

export const useAuth = () => {
  const router = useRouter();
  const userStore = useUserStore();

  // 加载状态
  const loading = ref(false);
  const loginLoading = ref(false);
  const registerLoading = ref(false);

  // 用户状态
  const isLoggedIn = computed(() => userStore.isLoggedIn);
  const userInfo = computed(() => userStore.userInfo);
  const token = computed(() => userStore.token);

  // 登录函数
  const handleLogin = async (credentials) => {
    if (loginLoading.value) return;

    loginLoading.value = true;
    try {
      const result = await Login(credentials);

      // 存储用户信息
      userStore.login(result);

      ElMessage.success('登录成功');

      // 跳转到目标页面或首页
      const redirect = router.currentRoute.value.query.redirect || '/';
      await router.replace(redirect);

      return result;
    } catch (error) {
      console.error('登录失败:', error);
      ElMessage.error('登录失败，请检查邮箱和密码');
      throw error;
    } finally {
      loginLoading.value = false;
    }
  };

  // 注册函数
  const handleRegister = async (credentials) => {
    if (registerLoading.value) return;

    registerLoading.value = true;
    try {
      const result = await Register(credentials);

      // 注册成功后可以直接登录
      userStore.login(result);

      ElMessage.success('注册成功');

      // 跳转到首页
      await router.replace('/');

      return result;
    } catch (error) {
      console.error('注册失败:', error);
      ElMessage.error('注册失败，请稍后重试');
      throw error;
    } finally {
      registerLoading.value = false;
    }
  };

  // 登出函数
  const handleLogout = async () => {
    if (loading.value) return;

    loading.value = true;
    try {
      // 调用后端登出接口（可选）
      try {
        await Logout();
      } catch (error) {
        console.warn('后端登出接口调用失败:', error);
      }

      // 清除本地状态
      userStore.logout();

      ElMessage.success('退出成功');

      // 跳转到登录页
      await router.replace('/login');
    } catch (error) {
      console.error('登出失败:', error);
      ElMessage.error('登出失败');
    } finally {
      loading.value = false;
    }
  };

  // 检查登录状态
  const checkAuth = () => {
    return isLoggedIn.value;
  };

  // 要求登录（如果未登录则跳转到登录页）
  const requireAuth = (message = '请先登录') => {
    if (!isLoggedIn.value) {
      ElMessage.warning(message);
      router.push({
        path: '/login',
        query: { redirect: router.currentRoute.value.fullPath },
      });
      return false;
    }
    return true;
  };

  return {
    // 状态
    loading,
    loginLoading,
    registerLoading,
    isLoggedIn,
    userInfo,
    token,

    // 方法
    handleLogin,
    handleRegister,
    handleLogout,
    checkAuth,
    requireAuth,
  };
};

import { ref, onMounted, onUnmounted } from 'vue';

export function useDevice() {
  const isMobile = ref(false);
  const isTablet = ref(false);
  const isDesktop = ref(false);
  const deviceType = ref('desktop'); // 'mobile', 'tablet', 'desktop'
  const screenWidth = ref(0);
  const screenHeight = ref(0);

  // 设备检测函数
  const detectDevice = () => {
    const width = window.innerWidth;
    const height = window.innerHeight;

    screenWidth.value = width;
    screenHeight.value = height;

    // 根据屏幕宽度判断设备类型
    if (width <= 768) {
      isMobile.value = true;
      isTablet.value = false;
      isDesktop.value = false;
      deviceType.value = 'mobile';
    } else if (width <= 1024) {
      isMobile.value = false;
      isTablet.value = true;
      isDesktop.value = false;
      deviceType.value = 'tablet';
    } else {
      isMobile.value = false;
      isTablet.value = false;
      isDesktop.value = true;
      deviceType.value = 'desktop';
    }
  };

  // 通过User Agent进行更精确的设备检测
  const getUserAgent = () => {
    const ua = navigator.userAgent.toLowerCase();
    const isMobileUA =
      /android|webos|iphone|ipad|ipod|blackberry|iemobile|opera mini/i.test(ua);
    const isTabletUA =
      /ipad|android(?=.*\smobile\s)/i.test(ua) && !/mobile/i.test(ua);

    return {
      isMobile: isMobileUA && !isTabletUA,
      isTablet: isTabletUA,
      isDesktop: !isMobileUA && !isTabletUA,
    };
  };

  // 综合检测 - 结合屏幕尺寸和User Agent
  const comprehensiveDetection = () => {
    const uaResult = getUserAgent();
    const width = window.innerWidth;

    // 如果UA检测到移动设备，优先使用UA结果（除非屏幕很大）
    if (uaResult.isMobile && width <= 768) {
      isMobile.value = true;
      isTablet.value = false;
      isDesktop.value = false;
      deviceType.value = 'mobile';
    } else if (uaResult.isTablet || (width > 768 && width <= 1024)) {
      isMobile.value = false;
      isTablet.value = true;
      isDesktop.value = false;
      deviceType.value = 'tablet';
    } else {
      isMobile.value = false;
      isTablet.value = false;
      isDesktop.value = true;
      deviceType.value = 'desktop';
    }

    screenWidth.value = width;
    screenHeight.value = window.innerHeight;
  };

  // 窗口大小改变处理函数
  const handleResize = () => {
    comprehensiveDetection();
  };

  // 获取断点信息
  const getBreakpoint = () => {
    const width = screenWidth.value;
    if (width < 576) return 'xs';
    if (width < 768) return 'sm';
    if (width < 992) return 'md';
    if (width < 1200) return 'lg';
    if (width < 1400) return 'xl';
    return 'xxl';
  };

  onMounted(() => {
    comprehensiveDetection();
    window.addEventListener('resize', handleResize);
  });

  onUnmounted(() => {
    window.removeEventListener('resize', handleResize);
  });

  return {
    // 响应式状态
    isMobile,
    isTablet,
    isDesktop,
    deviceType,
    screenWidth,
    screenHeight,

    // 方法
    detectDevice,
    getUserAgent,
    getBreakpoint,

    // 计算属性函数
    isSmallScreen: () => screenWidth.value <= 768,
    isMediumScreen: () => screenWidth.value > 768 && screenWidth.value <= 1024,
    isLargeScreen: () => screenWidth.value > 1024,
  };
}

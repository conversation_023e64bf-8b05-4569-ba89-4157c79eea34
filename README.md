# Ledger Web - 区块链项目前端

基于 Vue 3 + Vite + Element Plus 构建的现代化电商前端应用，集成了完整的支付系统。

## 技术栈

- **Vue 3** - 渐进式 JavaScript 框架
- **Vite** - 下一代前端构建工具
- **Element Plus** - Vue 3 组件库
- **Vue Router** - 官方路由管理器
- **Pinia** - Vue 状态管理
- **Sass** - CSS 预处理器
- **Axios** - HTTP 客户端

## 支付集成

项目已集成多种支付方式：

### ✅ Stripe 支付

- 完整的信用卡支付表单
- 符合 PCI DSS 标准
- 支持多种货币
- 实时表单验证
- 响应式设计

### 🔄 其他支付方式

- PayPal 支付
- 微信支付
- BitPay 加密货币支付

## 快速开始

### 1. 安装依赖

```bash
npm install
# 或
yarn install
```

### 2. 环境配置

复制环境变量文件：

```bash
cp .env.example .env
```

配置你的支付密钥：

```env
VITE_STRIPE_PUBLISHABLE_KEY=pk_test_your_stripe_key_here
```

### 3. 启动开发服务器

```bash
npm run dev
# 或
yarn dev
```

### 4. 构建生产版本

```bash
npm run build
# 或
yarn build
```

## 支付功能使用

### 访问支付页面

- 支付页面：`/pay`
- 支付成功页面：`/payment/success`

### Stripe 支付测试

在支付页面使用以下测试卡号：

- **成功支付**：4242 4242 4242 4242
- **需要验证**：4000 0025 0000 3155
- **被拒绝**：4000 0000 0000 0002

## 项目结构

```
src/
├── components/          # 公共组件
│   ├── StripePaymentForm.vue  # Stripe 支付表单
│   └── ...
├── pages/              # 页面组件
│   ├── Pay/            # 支付页面
│   ├── PaymentSuccess/ # 支付成功页面
│   ├── PaymentTest/    # 支付测试页面
│   └── ...
├── utils/              # 工具函数
│   ├── payment.js      # 支付工具类
│   └── ...
├── api/                # API 接口
│   ├── payment.js      # 支付相关接口
│   └── ...
└── stores/             # 状态管理
```

## 文档

- [Stripe 支付集成指南](./STRIPE_PAYMENT_GUIDE.md)
- [支付集成文档](./PAYMENT_INTEGRATION.md)

## 开发指南

### 代码规范

- 使用 ESLint + Prettier 进行代码格式化
- 遵循 Vue 3 Composition API 最佳实践
- 组件命名采用 PascalCase
- 文件命名采用 kebab-case

### 样式规范

- 使用 Sass 预处理器
- 采用 BEM 命名规范
- 响应式设计优先
- 统一的设计系统

## 浏览器支持

- Chrome >= 87
- Firefox >= 78
- Safari >= 14
- Edge >= 88

## 许可证

MIT License

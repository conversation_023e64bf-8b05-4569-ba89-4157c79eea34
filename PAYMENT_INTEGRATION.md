# 支付集成说明

本项目已集成四种主要支付方式：PayPal、Stripe、BitPay 和微信支付。

## 支付方式配置

### 1. PayPal 配置

1. 访问 [PayPal Developer](https://developer.paypal.com/)
2. 创建应用获取 Client ID
3. 在 `.env.local` 中配置：
```
VITE_PAYPAL_CLIENT_ID=your_paypal_client_id
VITE_PAYPAL_ENVIRONMENT=sandbox  # 或 production
```

### 2. Stripe 配置

1. 访问 [Stripe Dashboard](https://dashboard.stripe.com/)
2. 获取 Publishable Key
3. 在 `.env.local` 中配置：
```
VITE_STRIPE_PUBLISHABLE_KEY=pk_test_your_stripe_key
```

### 3. BitPay 配置

1. 访问 [BitPay](https://bitpay.com/)
2. 创建商户账户
3. 在 `.env.local` 中配置：
```
VITE_BITPAY_ENVIRONMENT=test  # 或 prod
VITE_BITPAY_API_URL=https://test.bitpay.com/api
```

### 4. 微信支付配置

1. 申请微信支付商户号
2. 获取 AppID
3. 在 `.env.local` 中配置：
```
VITE_WECHAT_APP_ID=your_wechat_app_id
VITE_WECHAT_ENVIRONMENT=sandbox
```

## 后端API接口

需要实现以下后端接口：

### PayPal
- `POST /v1/orders/{orderNumber}/pay/paypal` - 处理PayPal支付

### Stripe
- `POST /api/create-payment-intent` - 创建支付意图
- `POST /v1/orders/{orderNumber}/pay/stripe` - 处理Stripe支付

### BitPay
- `POST /api/bitpay/create-invoice` - 创建BitPay发票
- `POST /api/bitpay/webhook` - BitPay回调
- `POST /v1/orders/{orderNumber}/pay/bitpay` - 处理BitPay支付

### 微信支付
- `POST /api/wepay/create-order` - 创建微信支付订单
- `POST /v1/orders/{orderNumber}/pay/wepay` - 处理微信支付

### 通用接口
- `GET /v1/orders/{orderNumber}/pay/status` - 获取支付状态

## 使用方法

1. 在确认订单页面选择支付方式
2. 点击"去支付"按钮
3. 系统会根据选择的支付方式调用相应的支付处理函数
4. 支付完成后跳转到成功页面

## 支付流程

1. **PayPal**: 弹出PayPal支付窗口，用户完成支付后返回结果
2. **Stripe**: 使用Stripe Elements进行卡片支付
3. **BitPay**: 打开新窗口跳转到BitPay支付页面
4. **微信支付**: 显示二维码供用户扫描支付

## 注意事项

1. 所有支付方式都需要相应的后端API支持
2. 生产环境需要使用真实的API密钥
3. 需要配置正确的回调URL
4. 建议在测试环境充分测试后再部署到生产环境

## 安全建议

1. 敏感信息（如Secret Key）只能在后端使用
2. 前端只使用公开的Publishable Key
3. 所有支付回调都需要验证签名
4. 支付金额验证应在后端进行

# 🧾 API 接口文档（v1）

## 公共接口

### GET `/index`
首页信息

### GET `/products/{product}`
获取商品详情
- **描述**：获取商品详情
- **参数**：
    - `product_id`：商品ID（int）
- **返回**：商品详细信息
```json
{
  "status": "",
  "message": "",
  "data": {
    "product": {
      "id": "",
      "name": "",
      "description": "",
      "meta_title": "",
      "meta_keywords": "",
      "meta_description": "",
      "brand_id": "",
      "brand_name": "",
      "video": "",
      "images": {
        "preview": "",
        "popup": "",
        "thumb": ""
      },
      "attributes": "",
      "variables": "",
      "skus": [
        {
          "id": "",
          "product_id": "",
          "variants": "",
          "position": "",
          "images": "",
          "model": "",
          "sku": "",
          "price": "",
          "price_format": "",
          "origin_price": "",
          "origin_price_format": "",
          "quantity": "",
          "is_default": ""
        }
      ],
      "in_wishlist": "",
      "active": ""
    },
    "relations": ""
  }
}

```

### GET `/carts/mini`
获取迷你购物车信息

### GET `/orders/{number}`
获取订单详情
- **描述**：获取订单详情
- **参数**：
- **返回**：商品详细信息
```json
{
  "status": "",
  "message": "",
  "data": {
    "order": {
      "id": "",
      "number": "",
      "customer_id": "",
      "customer_group_id": "",
      "shipping_address_id": "",
      "payment_address_id": "",
      "customer_name": "",
      "email": "",
      "calling_code": "",
      "telephone": "",
      "total": "",
      "locale": "",
      "currency_code": "",
      "currency_value": "",
      "ip": "",
      "user_agent": "",
      "comment": "",
      "status": "",
      "shipping_method_code": "",
      "shipping_method_name": "",
      "shipping_customer_name": "",
      "shipping_calling_code": "",
      "shipping_telephone": "",
      "shipping_country": "",
      "shipping_country_id": "",
      "shipping_zone_id": "",
      "shipping_zone": "",
      "shipping_city": "",
      "shipping_address_1": "",
      "shipping_address_2": "",
      "shipping_zipcode": "",
      "payment_method_code": "",
      "payment_method_name": "",
      "payment_customer_name": "",
      "payment_calling_code": "",
      "payment_telephone": "",
      "payment_country": "",
      "payment_country_id": "",
      "payment_zone_id": "",
      "payment_zone": "",
      "payment_city": "",
      "payment_address_1": "",
      "payment_address_2": "",
      "payment_zipcode": "",
      "created_at": "",
      "updated_at": "",
      "deleted_at": "",
      "status_format": "",
      "total_format": "",
      "order_products": [
        {
          "id": "",
          "order_id": "",
          "product_id": "",
          "order_number": "",
          "product_sku": "",
          "name": "",
          "image": "",
          "quantity": "",
          "price": "",
          "created_at": "",
          "updated_at": "",
          "deleted_at": "",
          "price_format": ""
        }
      ],
      "order_totals": [
        {
          "id": "",
          "order_id": "",
          "code": "",
          "value": "",
          "title": "",
          "reference": "",
          "created_at": "",
          "updated_at": "",
          "value_format": ""
        }
      ],
      "order_histories": [
        {
          "id": "",
          "order_id": "",
          "status": "",
          "notify": "",
          "comment": "",
          "created_at": "",
          "updated_at": "",
          "status_format": ""
        }
      ]
    },
    "html_items": ""
  }
}

```


### POST `/login`
- **描述**：用户登录
- **参数**：
    - `email`：邮箱（string）
    - `password`：密码（string）
- **返回**：登录时间

### GET `/logout`
用户登出

### POST `/register`
- **描述**：用户注册
- **参数**：
    - `email`：邮箱（string）
    - `password`：密码（string）
- **返回**：

---

## 🛒 需要认证接口（购物车、结算、订单）

### GET `/carts`
- **描述**：获取购物车列表
- **参数**：
- **返回**：
```json
{
  "status": "",
  "message": "",
  "data": {
    "carts": [
      {
        "cart_id": "",
        "product_id": "",
        "sku_id": "",
        "sku": "",
        "product_sku": "",
        "name": "",
        "name_format": "",
        "image": "",
        "image_url": "",
        "quantity": "",
        "selected": "",
        "price": "",
        "shipping": "",
        "price_format": "",
        "tax_class_id": "",
        "subtotal": "",
        "subtotal_format": "",
        "variant_labels": ""
      }
    ],
    "quantity": "",
    "quantity_all": "",
    "amount": "",
    "amount_format": ""
  }
}

```

### POST `/carts`
- **描述**：添加商品到购物车
- **参数**：
    - `buy_now`：是否现在购买(false)
    - `quantity`:数量
    - `sku_id`: id
- **返回**：
 ```json
{
  "status": "",
  "message": "",
  "data": {
    "carts": [
      {
        "cart_id": "",
        "product_id": "",
        "sku_id": "",
        "sku": "",
        "product_sku": "",
        "name": "",
        "name_format": "",
        "image": "",
        "image_url": "",
        "quantity": "",
        "selected": "",
        "price": "",
        "shipping": "",
        "price_format": "",
        "tax_class_id": "",
        "subtotal": "",
        "subtotal_format": "",
        "variant_labels": ""
      }
    ],
    "quantity": "",
    "quantity_all": "",
    "amount": "",
    "amount_format": ""
  }
}

```


### PUT `/carts/{cart}`
- **描述**：更新购物车中的商品
- **参数**：
    - `quantity`:数量
    - `sku_id`: id
- **返回**：
 ```json
{
  "status": "",
  "message": "",
  "data": {
    "carts": [
      {
        "cart_id": "",
        "product_id": "",
        "sku_id": "",
        "sku": "",
        "product_sku": "",
        "name": "",
        "name_format": "",
        "image": "",
        "image_url": "",
        "quantity": "",
        "selected": "",
        "price": "",
        "shipping": "",
        "price_format": "",
        "tax_class_id": "",
        "subtotal": "",
        "subtotal_format": "",
        "variant_labels": ""
      }
    ],
    "quantity": "",
    "quantity_all": "",
    "amount": "",
    "amount_format": ""
  }
}

```

### POST `/carts/select`
- **描述**：选中购物车中的商品
- **参数**：
    - `cart_ids`: [] 选择的商品ID
- **返回**：
 ```json
{
  "status": "",
  "message": "",
  "data": {
    "carts": [
      {
        "cart_id": "",
        "product_id": "",
        "sku_id": "",
        "sku": "",
        "product_sku": "",
        "name": "",
        "name_format": "",
        "image": "",
        "image_url": "",
        "quantity": "",
        "selected": "",
        "price": "",
        "shipping": "",
        "price_format": "",
        "tax_class_id": "",
        "subtotal": "",
        "subtotal_format": "",
        "variant_labels": ""
      }
    ],
    "quantity": "",
    "quantity_all": "",
    "amount": "",
    "amount_format": ""
  }
}

```

### POST `/carts/unselect`
- **描述**：取消选中购物车商品
- **参数**：
    - `cart_ids`: [] 选择的商品ID
- **返回**：
 ```json
{
  "status": "",
  "message": "",
  "data": {
    "carts": [
      {
        "cart_id": "",
        "product_id": "",
        "sku_id": "",
        "sku": "",
        "product_sku": "",
        "name": "",
        "name_format": "",
        "image": "",
        "image_url": "",
        "quantity": "",
        "selected": "",
        "price": "",
        "shipping": "",
        "price_format": "",
        "tax_class_id": "",
        "subtotal": "",
        "subtotal_format": "",
        "variant_labels": ""
      }
    ],
    "quantity": "",
    "quantity_all": "",
    "amount": "",
    "amount_format": ""
  }
}

```

### DELETE `/v1/carts/{cart}`
删除购物车中的商品

### GET `/v1/checkout`
- **描述**：获取结算页信息
- **参数**：
- **返回**：
```json
{
    "status": "",
    "message": "",
    "data": {
        "carts": [
            {
                "cart_id": "",
                "product_id": "",
                "sku_id": "",
                "sku": "",
                "product_sku": "",
                "name": "",
                "name_format": "",
                "image": "",
                "image_url": "",
                "quantity": "",
                "selected": "",
                "price": "",
                "shipping": "",
                "price_format": "",
                "tax_class_id": "",
                "subtotal": "",
                "subtotal_format": "",
                "variant_labels": ""
            }
        ],
        "quantity": "",
        "quantity_all": "",
        "amount": "",
        "amount_format": ""
    }
}

```

### PUT `/v1/checkout`
- **描述**：更新结算信息
- **参数**：
  - `shipping_address_id`
  - `shipping_method_code`
  - `payment_address_id`
  - `payment_method_code`
  - `guest_shipping_address`
  - `guest_payment_address`
- **返回**：
```json lines
{
    "current": {
        "shipping_address_id": 0,
        "guest_shipping_address": 0,
        "shipping_method_code": "",
        "shipping_method_name": "",
        "payment_address_id": 0,
        "guest_payment_address": 0,
        "payment_method_code": "",
        "payment_method_name": "",
        "extra": 0
    },
    "shipping_require": null,
    "country_id": 0,
    "customer_id": 0,
    "Countries": [
        null
    ],
    "id": 0,
    "name": "",
    "continent_format": "",
    "Addresses": [
        null
    ],
    "phone": "",
    "country": "",
    "zone_id": 0,
    "zone": "",
    "city": "",
    "zipcode": "",
    "address_1": "",
    "address_2": "",
    "default": null,
    "ShippingMethods": [
        null
    ],
    "code": "",
    "Quotes": [
        null
    ],
    "type": "",
    "description": "",
    "icon": "",
    "cost": 0,
    "PaymentMethods": [
        null
    ],
    "carts": {
        "Carts": [
            null
        ],
        "cart_id": 0,
        "product_id": 0,
        "sku_id": 0,
        "sku": "",
        "product_sku": "",
        "name": "",
        "name_format": "",
        "image": "",
        "image_url": "",
        "quantity": 0,
        "selected": 0,
        "price": 0,
        "shipping": 0,
        "price_format": "",
        "tax_class_id": 0,
        "subtotal": 0,
        "subtotal_format": "",
        "variant_labels": ""
    },
    "quantity": 0,
    "quantity_all": 0,
    "amount": 0.1,
    "amount_format": "",
    "Totals": [
        null
    ],
    "title": ""
}
```

### GET `/v1/checkout/success`
- **描述**：结算成功页
- **参数**：
    - `order_number`
- **返回**：

### POST `/v1/checkout/confirm`
确认订单

### GET `/v1/orders/{number}/pay`
订单支付信息

### POST `/v1/orders/{number}/cancel`
取消订单

### POST `/v1/orders/{number}/complete`
完成订单

---

## 👤 用户账户模块（需登录）

### GET `/v1/account`
- **描述**：获取账户信息
- **参数**：
- **返回**：
```json lines
{
    "status": "",
    "message": "",
    "data": {
        "customer": {
            "id": "",
            "name": "",
            "email": "",
            "status": "",
            "avatar": "",
            "from": "",
            "customer_group_name": null
        },
        "latest_orders": [
            {
                "id": "",
                "number": "",
                "status_format": "",
                "next_status": [
                    {
                        "status": "",
                        "name": ""
                    }
                ],
                "status": ""
            }
        ]
    }
}

```

### 地址管理
### `GET /v1/account/addresses`
- **描述**：获取地址列表
- **参数**：
- **返回**：
```json lines

{
  "status": "",
  "message": "",
  "data": {
    "countries": [
      {
        "id": "",
        "name": "",
        "continent_format": ""
      }
    ],
    "addresses": [
      {
        "id": "",
        "name": "",
        "phone": "",
        "country_id": "",
        "country": "",
        "zone_id": "",
        "zone": "",
        "city": "",
        "zipcode": "",
        "address_1": "",
        "address_2": "",
        "default": false
      }
    ]
  }
}

```

### `POST /v1/account/addresses`
- **描述**：创建新地址
- **参数**：
  - `name`
  - `phone`
  - `country_id`
  - `zone_id`
  - `city_id`
  - `city`
  - `zipcode`
  - `address_1`
  - `address_2`
  - `default`
- **返回**：
```json lines
{
  "status": "",
  "message": "",
  "data": {
    "id": "",
    "name": "",
    "phone": "",
    "country_id": "",
    "country": "",
    "zone_id": "",
    "zone": "",
    "city": "",
    "zipcode": "",
    "address_1": "",
    "address_2": "",
    "default": false
  }
}

```

### `GET /v1/account/addresses/{address}` 
- **描述**：获取某个地址
- **参数**：
- **返回**：
```json lines
{
  "status": "",
  "message": "",
  "data": {
    "id": "",
    "name": "",
    "phone": "",
    "country_id": "",
    "country": "",
    "zone_id": "",
    "zone": "",
    "city": "",
    "zipcode": "",
    "address_1": "",
    "address_2": "",
    "default": false
  }
}

```

### `PUT /v1/account/addresses/{address}` 
- **描述**：更新地址
- **参数**：
    - `name`
    - `phone`
    - `country_id`
    - `zone_id`
    - `city_id`
    - `city`
    - `zipcode`
    - `address_1`
    - `address_2`
    - `default`
- **返回**：
```json lines
{
  "status": "",
  "message": "",
  "data": {
    "id": "",
    "name": "",
    "phone": "",
    "country_id": "",
    "country": "",
    "zone_id": "",
    "zone": "",
    "city": "",
    "zipcode": "",
    "address_1": "",
    "address_2": "",
    "default": false
  }
}

```

### `DELETE /v1/account/addresses/{address}`
删除地址

### 用户信息编辑
### `GET /v1/account/edit`
- **描述**：用户信息编辑,查询用户信息
```json lines
{
  "status": "",
  "message": "",
  "data": {
    "customer": {
      "id": "",
      "email": "",
      "password": "",
      "name": "",
      "avatar": "",
      "status": "",
      "customer_group_id": "",
      "address_id": "",
      "locale": "",
      "active": "",
      "code": "",
      "from": "",
      "deleted_at": null,
      "created_at": "",
      "updated_at": ""
    }
  }
}

```

### `PUT /v1/account/edit`
- **描述**：用户信息编辑
- **参数**：
  - `name`
  - `email`
  - `avatar`
- **返回**：
```json lines
{
  "status": "",
  "message": "",
  "data": {
    "customer": {
      "id": "",
      "email": "",
      "password": "",
      "name": "",
      "avatar": "",
      "status": "",
      "customer_group_id": "",
      "address_id": "",
      "locale": "",
      "active": "",
      "code": "",
      "from": "",
      "deleted_at": null,
      "created_at": "",
      "updated_at": ""
    }
  }
}

```
### 密码管理
- `GET /v1/account/password`
- `POST /v1/account/password`

### 订单售后（RMA）
- `GET /v1/account/rmas`
- `GET /v1/account/rmas/{id}`
- `GET /v1/account/rmas/create/{order_product_id}`
- `POST /v1/account/rmas/store`

### 用户订单
### `GET /v1/account/orders`
- **描述**：获取订单列表
- **参数**：
- **返回**：
```json lines
{
  "status": "",
  "message": "",
  "data": {
    "orders": [
      {
        "id": "",
        "number": "",
        "status_format": "",
        "next_status": [
          {
            "status": "",
            "name": ""
          }
        ],
        "status": ""
      }
    ]
  }
}


```

### `GET /v1/account/orders/{number}`
- **描述**：获取订单详情
- **参数**：
- **返回**：
```json lines
{
  "status": "",
  "message": "",
  "data": {
    "order": {
      "id": "",
      "number": "",
      "customer_id": "",
      "customer_group_id": "",
      "shipping_address_id": "",
      "payment_address_id": "",
      "customer_name": "",
      "email": "",
      "calling_code": "",
      "telephone": "",
      "total": "",
      "locale": "",
      "currency_code": "",
      "currency_value": "",
      "ip": "",
      "user_agent": "",
      "comment": "",
      "status": "",
      "shipping_method_code": "",
      "shipping_method_name": "",
      "shipping_customer_name": "",
      "shipping_calling_code": "",
      "shipping_telephone": "",
      "shipping_country": "",
      "shipping_country_id": "",
      "shipping_zone_id": "",
      "shipping_zone": "",
      "shipping_city": "",
      "shipping_address_1": "",
      "shipping_address_2": "",
      "shipping_zipcode": "",
      "payment_method_code": "",
      "payment_method_name": "",
      "payment_customer_name": "",
      "payment_calling_code": "",
      "payment_telephone": "",
      "payment_country": "",
      "payment_country_id": "",
      "payment_zone_id": "",
      "payment_zone": "",
      "payment_city": "",
      "payment_address_1": "",
      "payment_address_2": "",
      "payment_zipcode": "",
      "created_at": "",
      "updated_at": "",
      "deleted_at": null,
      "status_format": "",
      "total_format": "",
      "order_products": [
        {
          "id": "",
          "order_id": "",
          "product_id": "",
          "order_number": "",
          "product_sku": "",
          "name": "",
          "image": "",
          "quantity": "",
          "price": "",
          "created_at": "",
          "updated_at": "",
          "deleted_at": null,
          "price_format": ""
        }
      ],
      "order_totals": [
        {
          "id": "",
          "order_id": "",
          "code": "",
          "value": "",
          "title": "",
          "reference": "",
          "created_at": "",
          "updated_at": "",
          "value_format": ""
        }
      ],
      "order_histories": [
        {
          "id": "",
          "order_id": "",
          "status": "",
          "notify": "",
          "comment": "",
          "created_at": "",
          "updated_at": "",
          "status_format": ""
        }
      ]
    },
    "html_items": []
  }
}

```

### 修改密码
- `GET /v1/account/update_password`

### 我的收藏
### `GET /v1/account/wishlist`
- **描述**：获取我的收藏列表
- **参数**：
- **返回**：
```json lines
{
  "status": "",
  "message": "",
  "data": {
    "wishlist": [
      {
        "id": "",
        "product_id": "",
        "image": "",
        "product_name": "",
        "price": ""
      }
    ]
  }
}

```

### `POST /v1/account/wishlist`
- **描述**：添加首充
- **参数**：
  - `product_id`
- **返回**：
```json lines
{
    "status": "",
    "message": "",
    "data": {
        "product_id": "",
        "customer_id": "",
        "updated_at": "",
        "created_at": "",
        "id": ""
    }
}


```

- `DELETE /v1/account/wishlist/{id}`
- **描述**：删除收藏
- **参数**：
- **返回**：
```json lines
{
    "status": "",
    "message": "",
    "data": []
}
```

- `GET /v1/latest_products`
- **描述**：最新商品
- **参数**：
- **返回**：
```json lines
{
    "status": "",
    "message": "",
    "data": {
        "products": {
            "current_page": "",
            "data": [
                {
                    "id": "",
                    "sku_id": "",
                    "name": "",
                    "name_format": "",
                    "url": "",
                    "price": "",
                    "origin_price": "",
                    "price_format": "",
                    "origin_price_format": "",
                    "category_id": "",
                    "in_wishlist": "",
                    "images": [""]
                }
            ],
            "first_page_url": "",
            "from": "",
            "last_page": "",
            "last_page_url": "",
            "links": [
                {
                    "url": "",
                    "label": "",
                    "active": ""
                }
            ],
            "next_page_url": "",
            "path": "",
            "per_page": "",
            "prev_page_url": "",
            "to": "",
            "total": ""
        },
        "items": [
            {
                "id": "",
                "sku_id": "",
                "name": "",
                "name_format": "",
                "url": "",
                "price": "",
                "origin_price": "",
                "price_format": "",
                "origin_price_format": "",
                "category_id": "",
                "in_wishlist": "",
                "images": [""]
            }
        ]
    }
}

```


